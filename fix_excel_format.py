import pandas as pd
import re

def auto_detect_ioc_type(ioc_value):
    """Automatically detect IOC type based on the value"""
    ioc_value = str(ioc_value).strip()
    
    # URL patterns
    if ioc_value.startswith(('http://', 'https://', 'ftp://')):
        return 'url'
    
    # IP address pattern
    ip_pattern = r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$'
    if re.match(ip_pattern, ioc_value):
        return 'ip'
    
    # Hash patterns
    if len(ioc_value) == 32 and re.match(r'^[a-fA-F0-9]+$', ioc_value):
        return 'FileHash-MD5'
    elif len(ioc_value) == 40 and re.match(r'^[a-fA-F0-9]+$', ioc_value):
        return 'FileHash-SHA1'
    elif len(ioc_value) == 64 and re.match(r'^[a-fA-F0-9]+$', ioc_value):
        return 'FileHash-SHA256'
    
    # Domain/hostname patterns
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    if re.match(domain_pattern, ioc_value) and '.' in ioc_value:
        return 'domain'
    
    # Default to hostname if it looks like a network identifier
    return 'hostname'

def fix_excel_format(input_file, output_file=None):
    """Convert single-column Excel to proper IOC format"""
    try:
        # Read the Excel file
        df = pd.read_excel(input_file)
        
        print(f"Original file analysis:")
        print(f"- Columns: {len(df.columns)}")
        print(f"- Rows: {len(df)}")
        print(f"- Column names: {list(df.columns)}")
        
        if len(df.columns) >= 2:
            print("✅ File already has 2+ columns. No conversion needed.")
            return input_file
        
        # Get the single column data
        first_col = df.columns[0]
        ioc_values = df[first_col].dropna().tolist()
        
        print(f"\nConverting {len(ioc_values)} IOC values...")
        
        # Create new structured data
        new_data = []
        for ioc in ioc_values:
            ioc_type = auto_detect_ioc_type(ioc)
            new_data.append({
                'Type': ioc_type,
                'IOC': str(ioc).strip()
            })
        
        # Create new DataFrame
        new_df = pd.DataFrame(new_data)
        
        # Set output filename
        if output_file is None:
            base_name = input_file.replace('.xlsx', '').replace('.xls', '')
            output_file = f"{base_name}_fixed.xlsx"
        
        # Save the fixed file
        new_df.to_excel(output_file, index=False)
        
        print(f"\n✅ Fixed file saved as: {output_file}")
        print(f"\nNew file structure:")
        print(f"- Columns: {len(new_df.columns)} (Type, IOC)")
        print(f"- Rows: {len(new_df)}")
        
        # Show type distribution
        type_counts = new_df['Type'].value_counts()
        print(f"\nIOC Type Distribution:")
        for ioc_type, count in type_counts.items():
            print(f"  {ioc_type}: {count}")
        
        print(f"\nFirst few rows of fixed file:")
        print(new_df.head())
        
        return output_file
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        return None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None
        fix_excel_format(input_file, output_file)
    else:
        print("Usage: python fix_excel_format.py <input_excel_file> [output_excel_file]")
        print("Example: python fix_excel_format.py 'scattered spider.xlsx'")
