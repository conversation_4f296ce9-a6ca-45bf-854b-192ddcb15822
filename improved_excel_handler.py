import pandas as pd

def analyze_excel_structure(excel_file):
    """Analyze Excel file structure and suggest fixes"""
    try:
        # Try to read the Excel file
        df = pd.read_excel(excel_file)
        
        print(f"Excel file analysis:")
        print(f"- Number of columns: {len(df.columns)}")
        print(f"- Number of rows: {len(df)}")
        print(f"- Column names: {list(df.columns)}")
        print(f"\nFirst few rows:")
        print(df.head())
        
        # Check if it has at least 2 columns
        if len(df.columns) < 2:
            print(f"\n❌ ERROR: Excel file has only {len(df.columns)} column(s). Need at least 2 columns.")
            print("SOLUTION: Add a second column or restructure your data.")
            return False
            
        # Check for common column name patterns
        col_names = [str(col).lower() for col in df.columns]
        
        # Suggest column mapping
        print(f"\n✅ File has {len(df.columns)} columns - minimum requirement met!")
        print(f"Current column mapping will be:")
        print(f"- Type column: '{df.columns[0]}' (1st column)")
        print(f"- IOC column: '{df.columns[1]}' (2nd column)")
        
        # Check for empty values
        type_col_empty = df.iloc[:, 0].isna().sum()
        ioc_col_empty = df.iloc[:, 1].isna().sum()
        
        if type_col_empty > 0:
            print(f"⚠️  WARNING: {type_col_empty} empty values in Type column")
        if ioc_col_empty > 0:
            print(f"⚠️  WARNING: {ioc_col_empty} empty values in IOC column")
            
        return True
        
    except Exception as e:
        print(f"❌ ERROR reading Excel file: {e}")
        return False

def create_template_excel():
    """Create a template Excel file"""
    template_data = {
        'Type': [
            'domain',
            'ip', 
            'url',
            'FileHash-MD5',
            'FileHash-SHA256',
            'hostname'
        ],
        'IOC': [
            'example-domain.com',
            '***********',
            'http://example-url.com/path',
            'a1b2c3d4e5f67890123456789012345678901234',
            'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
            'hostname.example.com'
        ]
    }
    
    df = pd.DataFrame(template_data)
    df.to_excel('ioc_template.xlsx', index=False)
    print("✅ Template file 'ioc_template.xlsx' created!")
    return 'ioc_template.xlsx'

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
        print(f"Analyzing: {excel_file}")
        analyze_excel_structure(excel_file)
    else:
        print("Creating template Excel file...")
        create_template_excel()
        print("\nUsage: python improved_excel_handler.py <excel_file_path>")
