"""
IOC Analysis Tool Launcher
Checks configuration and launches the appropriate interface
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    missing_deps = []
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import openpyxl
    except ImportError:
        missing_deps.append("openpyxl")
    
    try:
        import tqdm
    except ImportError:
        missing_deps.append("tqdm")
    
    if missing_deps:
        print("❌ Missing required dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\nInstall them with:")
        print(f"   pip install {' '.join(missing_deps)}")
        print("\nOr install all requirements:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def check_configuration():
    """Check if configuration is valid"""
    try:
        from config import get_config
        config_manager = get_config()
        return config_manager.validate_configuration()
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        return False

def show_welcome():
    """Show welcome message"""
    print("🚀 IOC Analysis Tool v2.0")
    print("=" * 40)
    print("Advanced IOC Analysis with VirusTotal Integration")
    print()

def show_configuration_help():
    """Show configuration help"""
    print("⚙️  Configuration Required")
    print("=" * 30)
    print()
    print("To use this tool, you need to configure your VirusTotal API keys.")
    print()
    print("Options:")
    print("1. Run the setup wizard: python setup_config.py")
    print("2. Manual setup:")
    print("   - Edit config/api_keys.json")
    print("   - Replace placeholder keys with your actual VirusTotal API keys")
    print("   - Get keys from: https://www.virustotal.com/gui/my-apikey")
    print()

def show_usage_options():
    """Show usage options"""
    print("🎯 Usage Options")
    print("=" * 20)
    print()
    print("1. GUI Application (Recommended for beginners):")
    print("   python modern_gui.py")
    print()
    print("2. Command Line Interface (For automation):")
    print("   python cli_interface.py input.xlsx output.xlsx")
    print()
    print("3. Run Tests:")
    print("   python test_ioc_analyzer.py")
    print()

def interactive_launcher():
    """Interactive launcher menu"""
    while True:
        print("\nWhat would you like to do?")
        print("1. Launch GUI Application")
        print("2. Launch CLI Application")
        print("3. Run Configuration Setup")
        print("4. Run Tests")
        print("5. Show Help")
        print("6. Exit")
        
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == "1":
            print("\n🖥️  Launching GUI Application...")
            try:
                import modern_gui
                modern_gui.main()
            except Exception as e:
                print(f"❌ Failed to launch GUI: {e}")
            break
        
        elif choice == "2":
            print("\n⌨️  CLI Application requires input and output file paths.")
            input_file = input("Enter input Excel file path: ").strip()
            output_file = input("Enter output file path: ").strip()
            
            if input_file and output_file:
                print(f"\n⌨️  Launching CLI Application...")
                try:
                    import cli_interface
                    sys.argv = ["cli_interface.py", input_file, output_file]
                    cli_interface.main()
                except Exception as e:
                    print(f"❌ Failed to launch CLI: {e}")
            else:
                print("❌ Both input and output file paths are required.")
            break
        
        elif choice == "3":
            print("\n⚙️  Launching Configuration Setup...")
            try:
                import setup_config
                setup_config.main()
            except Exception as e:
                print(f"❌ Failed to launch setup: {e}")
        
        elif choice == "4":
            print("\n🧪 Running Tests...")
            try:
                import test_ioc_analyzer
                test_ioc_analyzer.run_tests()
            except Exception as e:
                print(f"❌ Failed to run tests: {e}")
        
        elif choice == "5":
            show_usage_options()
            show_configuration_help()
        
        elif choice == "6":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice. Please enter 1-6.")

def main():
    """Main launcher function"""
    show_welcome()
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    print("✅ All dependencies are installed")
    
    # Check configuration
    config_valid = check_configuration()
    
    if config_valid:
        print("✅ Configuration is valid")
        print()
        
        # If command line arguments provided, try to launch CLI
        if len(sys.argv) > 1:
            if sys.argv[1] == "--gui":
                print("🖥️  Launching GUI Application...")
                try:
                    import modern_gui
                    modern_gui.main()
                    return 0
                except Exception as e:
                    print(f"❌ Failed to launch GUI: {e}")
                    return 1
            
            elif sys.argv[1] == "--cli" and len(sys.argv) >= 4:
                print("⌨️  Launching CLI Application...")
                try:
                    import cli_interface
                    # Pass through arguments
                    sys.argv = ["cli_interface.py"] + sys.argv[2:]
                    cli_interface.main()
                    return 0
                except Exception as e:
                    print(f"❌ Failed to launch CLI: {e}")
                    return 1
            
            elif sys.argv[1] == "--test":
                print("🧪 Running Tests...")
                try:
                    import test_ioc_analyzer
                    success = test_ioc_analyzer.run_tests()
                    return 0 if success else 1
                except Exception as e:
                    print(f"❌ Failed to run tests: {e}")
                    return 1
            
            else:
                print("❌ Invalid arguments")
                show_usage_options()
                return 1
        
        else:
            # Interactive mode
            show_usage_options()
            interactive_launcher()
            return 0
    
    else:
        print("⚠️  Configuration is not valid")
        show_configuration_help()
        
        # Ask if user wants to run setup
        setup = input("Run configuration setup now? (y/n): ").lower()
        if setup == 'y':
            try:
                import setup_config
                setup_config.main()
                
                # Check configuration again
                if check_configuration():
                    print("\n✅ Configuration is now valid!")
                    show_usage_options()
                    interactive_launcher()
                    return 0
                else:
                    print("\n❌ Configuration is still invalid. Please check your settings.")
                    return 1
            except Exception as e:
                print(f"❌ Setup failed: {e}")
                return 1
        else:
            print("Please configure the tool before using it.")
            return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
