"""
Command Line Interface for IOC Analysis Tool
Provides a CLI alternative to the GUI interface
"""

import argparse
import sys
import json
from pathlib import Path
from typing import Optional, List
import logging

from config import get_config
from error_handler import get_error_handler, UserFriendlyErrorReporter
from ioc_processor import IOCExtractor, VirusTotalClient, IOCType
from api_manager import SecureAPIKeyManager

# Configure logging for CLI
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CLIInterface:
    """Command line interface for IOC analysis"""
    
    def __init__(self):
        self.config = get_config()
        self.error_handler = get_error_handler()
        self.error_reporter = UserFriendlyErrorReporter()
    
    def run_analysis(self, input_file: str, output_file: str, 
                    selected_vendors: Optional[List[str]] = None,
                    max_workers: Optional[int] = None) -> bool:
        """
        Run IOC analysis from command line
        
        Args:
            input_file: Path to input Excel file
            output_file: Path to output Excel file
            selected_vendors: Optional list of vendors to filter
            max_workers: Optional number of worker threads
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate configuration
            if not self.config.validate_configuration():
                print("❌ Configuration validation failed. Please check your configuration files.")
                return False
            
            # Load settings
            settings = self.config.get_settings()
            if max_workers:
                settings['max_workers'] = max_workers
            
            print(f"🔍 Starting IOC analysis...")
            print(f"📁 Input file: {input_file}")
            print(f"💾 Output file: {output_file}")
            
            # Initialize components
            print("⚙️  Initializing components...")
            api_keys = self.config.get_api_keys()
            api_manager = SecureAPIKeyManager(api_keys, settings.get('api_delay', 2))
            extractor = IOCExtractor()
            vt_client = VirusTotalClient(api_manager, settings.get('request_timeout', 30))
            
            # Extract IOCs
            print("📊 Extracting IOCs from Excel file...")
            iocs, total_read, duplicates_skipped = extractor.extract_from_excel(input_file)
            
            if not any(iocs.values()):
                print("❌ No valid IOCs found in the input file.")
                return False
            
            total_iocs = sum(len(ioc_list) for ioc_list in iocs.values())
            print(f"✅ Extracted {total_iocs} unique IOCs from {total_read} rows")
            print(f"🔄 Skipped {duplicates_skipped} duplicates")
            
            # Display IOC breakdown
            print("\n📈 IOC Type Breakdown:")
            for ioc_type, ioc_list in iocs.items():
                if ioc_list:
                    print(f"  {ioc_type}: {len(ioc_list)}")
            
            # Analyze IOCs
            print(f"\n🔬 Analyzing IOCs with VirusTotal...")
            if selected_vendors and selected_vendors != "All":
                print(f"🎯 Using {len(selected_vendors)} selected vendors")
            else:
                print("🎯 Using all available vendors")
            
            results = self._analyze_iocs_concurrent(iocs, vt_client, selected_vendors, settings)
            
            if not results:
                print("❌ No analysis results obtained.")
                return False
            
            # Save results
            print(f"\n💾 Saving results to {output_file}...")
            self._save_results(results, output_file, total_read, duplicates_skipped)
            
            # Display summary
            self._display_summary(results)
            
            print(f"\n✅ Analysis completed successfully!")
            print(f"📄 Results saved to: {output_file}")
            
            return True
            
        except Exception as e:
            error_msg = self.error_reporter.get_user_message(e)
            print(f"❌ Error: {error_msg}")
            self.error_handler.handle_error(e, {
                "input_file": input_file,
                "output_file": output_file,
                "interface": "CLI"
            })
            return False
    
    def _analyze_iocs_concurrent(self, iocs, vt_client, selected_vendors, settings):
        """Analyze IOCs concurrently"""
        from concurrent.futures import ThreadPoolExecutor, as_completed
        from tqdm import tqdm
        
        results = []
        total_iocs = sum(len(ioc_list) for ioc_list in iocs.values())
        max_workers = settings.get('max_workers', 10)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all IOCs for analysis
            future_to_ioc = {}
            for ioc_type_str, ioc_list in iocs.items():
                ioc_type = IOCType(ioc_type_str)
                for ioc in ioc_list:
                    future = executor.submit(vt_client.analyze_ioc, ioc, ioc_type, selected_vendors)
                    future_to_ioc[future] = (ioc, ioc_type)
            
            # Process results with progress bar
            with tqdm(total=total_iocs, desc="Analyzing IOCs", unit="IOC") as pbar:
                for future in as_completed(future_to_ioc):
                    ioc, ioc_type = future_to_ioc[future]
                    try:
                        result = future.result()
                        results.append({
                            "Type": result.ioc_type,
                            "IOC": result.ioc,
                            "Result": result.result,
                            "Severity": result.severity,
                            "Filename": result.filename,
                            "Detected By": ", ".join(result.detected_by_vendors) if result.detected_by_vendors else "Not Detected"
                        })
                    except Exception as e:
                        logger.error(f"Failed to analyze {ioc}: {e}")
                        results.append({
                            "Type": ioc_type.value,
                            "IOC": ioc,
                            "Result": f"Analysis Error: {e}",
                            "Severity": "Low",
                            "Filename": "Unknown",
                            "Detected By": "Error"
                        })
                    finally:
                        pbar.update(1)
        
        return results
    
    def _save_results(self, results, output_file, total_read, duplicates_skipped):
        """Save analysis results to Excel file"""
        import pandas as pd
        
        # Create DataFrame
        df_results = pd.DataFrame(results)
        
        # Remove duplicates based on IOC value
        if not df_results.empty:
            df_results.drop_duplicates(subset=['IOC'], keep='first', inplace=True)
        
        # Separate by severity
        severity_columns = ["Type", "IOC", "Filename", "Detected By", "Severity"]
        high_severity_df = df_results[df_results["Severity"] == "High"][severity_columns].copy()
        medium_severity_df = df_results[df_results["Severity"] == "Medium"][severity_columns].copy()
        low_severity_df = df_results[df_results["Severity"] == "Low"][severity_columns].copy()
        very_low_severity_df = df_results[df_results["Severity"] == "Very Low"][severity_columns].copy()
        
        # Create summary
        total_processed = len(df_results)
        successful_count = len(df_results[~df_results["Result"].str.contains("Error", na=False)])
        error_count = len(df_results[df_results["Result"].str.contains("Error", na=False)])
        not_found_count = len(df_results[df_results["Result"].str.contains("Not found", na=False)])
        
        summary_data = {
            "Metric": [
                "Total IOC Rows Read from Input",
                "Duplicate IOC Values Skipped",
                "Total IOCs After Deduplication",
                "IOCs Successfully Analyzed",
                "IOCs Not Found in VirusTotal",
                "Errors During Analysis",
                "High Severity IOCs",
                "Medium Severity IOCs", 
                "Low Severity IOCs",
                "Very Low Severity IOCs"
            ],
            "Count": [
                total_read,
                duplicates_skipped,
                total_processed,
                successful_count,
                not_found_count,
                error_count,
                len(high_severity_df),
                len(medium_severity_df),
                len(low_severity_df),
                len(very_low_severity_df)
            ]
        }
        df_summary = pd.DataFrame(summary_data)
        
        # Write to Excel
        with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
            # Write severity breakdown
            current_row = 0
            sheet_name = "IOC Severity Breakdown"
            
            # High severity
            if not high_severity_df.empty:
                pd.DataFrame([["High Severity IOCs (Malicious)"]]).to_excel(
                    writer, sheet_name=sheet_name, startrow=current_row, index=False, header=False
                )
                current_row += 1
                high_severity_df.to_excel(writer, sheet_name=sheet_name, startrow=current_row, index=False)
                current_row += len(high_severity_df) + 2
            
            # Medium severity
            if not medium_severity_df.empty:
                pd.DataFrame([["Medium Severity IOCs (Suspicious)"]]).to_excel(
                    writer, sheet_name=sheet_name, startrow=current_row, index=False, header=False
                )
                current_row += 1
                medium_severity_df.to_excel(writer, sheet_name=sheet_name, startrow=current_row, index=False)
                current_row += len(medium_severity_df) + 2
            
            # Low severity
            if not low_severity_df.empty:
                pd.DataFrame([["Low Severity IOCs (Unknown/Errors)"]]).to_excel(
                    writer, sheet_name=sheet_name, startrow=current_row, index=False, header=False
                )
                current_row += 1
                low_severity_df.to_excel(writer, sheet_name=sheet_name, startrow=current_row, index=False)
                current_row += len(low_severity_df) + 2
            
            # Very low severity
            if not very_low_severity_df.empty:
                pd.DataFrame([["Very Low Severity IOCs (Clean/Legitimate)"]]).to_excel(
                    writer, sheet_name=sheet_name, startrow=current_row, index=False, header=False
                )
                current_row += 1
                very_low_severity_df.to_excel(writer, sheet_name=sheet_name, startrow=current_row, index=False)
            
            # Write summary
            df_summary.to_excel(writer, sheet_name="Processing Summary", index=False)
    
    def _display_summary(self, results):
        """Display analysis summary"""
        df = pd.DataFrame(results)
        
        print("\n📊 Analysis Summary:")
        print(f"  Total IOCs Processed: {len(df)}")
        
        # Severity breakdown
        severity_counts = df['Severity'].value_counts()
        for severity, count in severity_counts.items():
            print(f"  {severity} Severity: {count}")
        
        # Top detected vendors
        all_vendors = []
        for detected_by in df['Detected By']:
            if detected_by and detected_by != "Not Detected" and detected_by != "Error":
                all_vendors.extend([v.strip() for v in detected_by.split(',')])
        
        if all_vendors:
            from collections import Counter
            top_vendors = Counter(all_vendors).most_common(5)
            print(f"\n🏆 Top Detecting Vendors:")
            for vendor, count in top_vendors:
                print(f"  {vendor}: {count} detections")

def create_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="IOC Analysis Tool - Command Line Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s input.xlsx output.xlsx
  %(prog)s input.xlsx output.xlsx --vendors Microsoft Google Kaspersky
  %(prog)s input.xlsx output.xlsx --max-workers 5 --verbose
        """
    )
    
    parser.add_argument(
        "input_file",
        help="Path to input Excel file containing IOCs"
    )
    
    parser.add_argument(
        "output_file", 
        help="Path to output Excel file for results"
    )
    
    parser.add_argument(
        "--vendors",
        nargs="*",
        help="Specific vendors to use for analysis (default: all vendors)"
    )
    
    parser.add_argument(
        "--max-workers",
        type=int,
        help="Maximum number of worker threads (default: from config)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--config-dir",
        help="Path to configuration directory (default: ./config)"
    )
    
    parser.add_argument(
        "--validate-config",
        action="store_true",
        help="Validate configuration and exit"
    )
    
    return parser

def main():
    """Main CLI entry point"""
    parser = create_parser()
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize CLI interface
    cli = CLIInterface()
    
    # Validate configuration if requested
    if args.validate_config:
        if cli.config.validate_configuration():
            print("✅ Configuration is valid")
            sys.exit(0)
        else:
            print("❌ Configuration validation failed")
            sys.exit(1)
    
    # Validate input file exists
    if not Path(args.input_file).exists():
        print(f"❌ Input file not found: {args.input_file}")
        sys.exit(1)
    
    # Create output directory if needed
    output_path = Path(args.output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Run analysis
    success = cli.run_analysis(
        input_file=args.input_file,
        output_file=args.output_file,
        selected_vendors=args.vendors,
        max_workers=args.max_workers
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
