"""
Setup script for IOC Analysis Tool
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "IOC Analysis Tool - Analyze Indicators of Compromise using VirusTotal"

# Read requirements
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="ioc-analysis-tool",
    version="2.0.0",
    author="CTI Techlab",
    author_email="<EMAIL>",
    description="Advanced IOC Analysis Tool with VirusTotal Integration",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/ctitechlab/ioc-analysis-tool",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Information Technology",
        "Topic :: Security",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-mock>=3.8.0",
            "mypy>=0.991",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "isort>=5.10.0",
        ],
        "docs": [
            "sphinx>=5.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
        "security": [
            "bandit>=1.7.0",
            "safety>=2.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "ioc-analyzer=modern_gui:main",
            "ioc-analyzer-cli=cli_interface:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.yaml", "*.yml", "*.txt"],
    },
    project_urls={
        "Bug Reports": "https://github.com/ctitechlab/ioc-analysis-tool/issues",
        "Source": "https://github.com/ctitechlab/ioc-analysis-tool",
        "Documentation": "https://ioc-analysis-tool.readthedocs.io/",
    },
)
