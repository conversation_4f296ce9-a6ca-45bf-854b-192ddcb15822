"""
Secure API Key Management for IOC Analysis Tool
Handles API key rotation, rate limiting, and error handling
"""

import threading
import time
import logging
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class APIKeyStatus(Enum):
    """API Key status enumeration"""
    ACTIVE = "active"
    EXHAUSTED = "exhausted"
    INVALID = "invalid"
    RATE_LIMITED = "rate_limited"

@dataclass
class APIKeyInfo:
    """Information about an API key"""
    key: str
    status: APIKeyStatus
    last_used: float
    error_count: int
    requests_made: int
    
    def __post_init__(self):
        """Initialize timestamps"""
        if self.last_used == 0:
            self.last_used = time.time()

class SecureAPIKeyManager:
    """Thread-safe API key manager with rotation and rate limiting"""
    
    def __init__(self, api_keys: List[str], rate_limit_delay: float = 2.0):
        """
        Initialize API key manager
        
        Args:
            api_keys: List of API keys to manage
            rate_limit_delay: Minimum delay between API calls
        """
        if not api_keys:
            raise ValueError("At least one API key must be provided")
        
        self.rate_limit_delay = rate_limit_delay
        self.lock = threading.Lock()
        self.current_index = 0
        
        # Initialize API key information
        self.api_keys: List[APIKeyInfo] = [
            APIKeyInfo(
                key=key,
                status=APIKeyStatus.ACTIVE,
                last_used=0,
                error_count=0,
                requests_made=0
            )
            for key in api_keys
        ]
        
        logger.info(f"Initialized API key manager with {len(self.api_keys)} keys")
    
    def get_api_key(self) -> Optional[str]:
        """
        Get the next available API key with rate limiting

        Returns:
            API key string or None if no keys available
        """
        with self.lock:
            # Check if any keys are available
            active_keys = [key_info for key_info in self.api_keys
                          if key_info.status == APIKeyStatus.ACTIVE]

            if not active_keys:
                logger.error("No active API keys available")
                return None

            # Find the next key to use (round-robin with rate limiting)
            attempts = 0
            max_attempts = len(self.api_keys)

            while attempts < max_attempts:
                key_info = self.api_keys[self.current_index]

                # Check if key is active and rate limit is satisfied
                if (key_info.status == APIKeyStatus.ACTIVE and
                    time.time() - key_info.last_used >= self.rate_limit_delay):

                    # Update key usage
                    key_info.last_used = time.time()
                    key_info.requests_made += 1

                    # Move to next key for next request
                    self.current_index = (self.current_index + 1) % len(self.api_keys)

                    logger.debug(f"Using API key ending in ...{key_info.key[-8:]}")
                    return key_info.key

                # Move to next key
                self.current_index = (self.current_index + 1) % len(self.api_keys)
                attempts += 1

            # If we get here, all active keys are rate limited
            # Return None to indicate no keys available right now
            return None
    
    def mark_key_invalid(self, api_key: str, reason: str = "Invalid key") -> None:
        """
        Mark an API key as invalid
        
        Args:
            api_key: The API key to mark as invalid
            reason: Reason for marking as invalid
        """
        with self.lock:
            for key_info in self.api_keys:
                if key_info.key == api_key:
                    key_info.status = APIKeyStatus.INVALID
                    key_info.error_count += 1
                    logger.warning(f"Marked API key as invalid: {reason}")
                    break
    
    def mark_key_exhausted(self, api_key: str, reason: str = "Quota exhausted") -> None:
        """
        Mark an API key as quota exhausted
        
        Args:
            api_key: The API key to mark as exhausted
            reason: Reason for marking as exhausted
        """
        with self.lock:
            for key_info in self.api_keys:
                if key_info.key == api_key:
                    key_info.status = APIKeyStatus.EXHAUSTED
                    key_info.error_count += 1
                    logger.warning(f"Marked API key as exhausted: {reason}")
                    break
    
    def mark_key_rate_limited(self, api_key: str, cooldown_seconds: int = 60) -> None:
        """
        Temporarily mark an API key as rate limited
        
        Args:
            api_key: The API key to mark as rate limited
            cooldown_seconds: How long to wait before retrying
        """
        with self.lock:
            for key_info in self.api_keys:
                if key_info.key == api_key:
                    key_info.status = APIKeyStatus.RATE_LIMITED
                    key_info.last_used = time.time() + cooldown_seconds
                    logger.info(f"API key rate limited for {cooldown_seconds} seconds")
                    
                    # Schedule reactivation
                    def reactivate():
                        time.sleep(cooldown_seconds)
                        with self.lock:
                            if key_info.status == APIKeyStatus.RATE_LIMITED:
                                key_info.status = APIKeyStatus.ACTIVE
                                logger.info("API key reactivated after rate limit")
                    
                    threading.Thread(target=reactivate, daemon=True).start()
                    break
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get usage statistics for all API keys
        
        Returns:
            Dictionary containing usage statistics
        """
        with self.lock:
            stats = {
                "total_keys": len(self.api_keys),
                "active_keys": len([k for k in self.api_keys if k.status == APIKeyStatus.ACTIVE]),
                "exhausted_keys": len([k for k in self.api_keys if k.status == APIKeyStatus.EXHAUSTED]),
                "invalid_keys": len([k for k in self.api_keys if k.status == APIKeyStatus.INVALID]),
                "rate_limited_keys": len([k for k in self.api_keys if k.status == APIKeyStatus.RATE_LIMITED]),
                "total_requests": sum(k.requests_made for k in self.api_keys),
                "total_errors": sum(k.error_count for k in self.api_keys),
                "keys_detail": []
            }
            
            for i, key_info in enumerate(self.api_keys):
                stats["keys_detail"].append({
                    "index": i,
                    "key_suffix": key_info.key[-8:],
                    "status": key_info.status.value,
                    "requests_made": key_info.requests_made,
                    "error_count": key_info.error_count,
                    "last_used": key_info.last_used
                })
            
            return stats
    
    def has_active_keys(self) -> bool:
        """
        Check if there are any active API keys available
        
        Returns:
            True if active keys are available, False otherwise
        """
        with self.lock:
            return any(key_info.status == APIKeyStatus.ACTIVE for key_info in self.api_keys)
    
    def reset_key_status(self, api_key: str) -> bool:
        """
        Reset an API key status to active (for testing or manual recovery)
        
        Args:
            api_key: The API key to reset
            
        Returns:
            True if key was found and reset, False otherwise
        """
        with self.lock:
            for key_info in self.api_keys:
                if key_info.key == api_key:
                    key_info.status = APIKeyStatus.ACTIVE
                    key_info.error_count = 0
                    key_info.last_used = 0
                    logger.info(f"Reset API key status to active")
                    return True
            return False
    
    def wait_for_available_key(self, timeout: int = 300) -> Optional[str]:
        """
        Wait for an available API key with timeout
        
        Args:
            timeout: Maximum time to wait in seconds
            
        Returns:
            API key if available within timeout, None otherwise
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            key = self.get_api_key()
            if key:
                return key
            
            # Wait a bit before trying again
            time.sleep(min(self.rate_limit_delay, 5))
        
        logger.error(f"Timeout waiting for available API key after {timeout} seconds")
        return None
