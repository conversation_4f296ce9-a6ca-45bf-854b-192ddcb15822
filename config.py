"""
Configuration management for IOC Analysis Tool
Handles secure loading of API keys and application settings
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConfigManager:
    """Secure configuration manager for IOC analysis tool"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # Configuration file paths
        self.api_keys_file = self.config_dir / "api_keys.json"
        self.settings_file = self.config_dir / "settings.json"
        self.vendors_file = self.config_dir / "vendors.json"
        
        # Default settings
        self.default_settings = {
            "max_workers": 10,
            "api_delay": 2,
            "request_timeout": 30,
            "max_retries": 3,
            "output_format": "xlsx",
            "debug_mode": False
        }
        
        # Initialize configuration files if they don't exist
        self._initialize_config_files()
    
    def _initialize_config_files(self) -> None:
        """Initialize configuration files with default values"""
        
        # Initialize API keys file
        if not self.api_keys_file.exists():
            self._create_api_keys_template()
        
        # Initialize settings file
        if not self.settings_file.exists():
            self._save_json(self.settings_file, self.default_settings)
            logger.info(f"Created default settings file: {self.settings_file}")
        
        # Initialize vendors file
        if not self.vendors_file.exists():
            self._create_vendors_file()
    
    def _create_api_keys_template(self) -> None:
        """Create API keys template file"""
        template = {
            "virustotal_api_keys": [
                "YOUR_API_KEY_1_HERE",
                "YOUR_API_KEY_2_HERE"
            ],
            "instructions": [
                "Replace the placeholder keys with your actual VirusTotal API keys",
                "You can add multiple keys for better rate limiting",
                "Never commit this file to version control",
                "Keep your API keys secure and private"
            ]
        }
        self._save_json(self.api_keys_file, template)
        logger.warning(f"Created API keys template: {self.api_keys_file}")
        logger.warning("Please update the API keys file with your actual keys!")
    
    def _create_vendors_file(self) -> None:
        """Create vendors configuration file"""
        vendors_config = {
            "common_vendors": [
                "Microsoft", "Google", "Kaspersky", "McAfee", "Symantec", 
                "CrowdStrike", "Palo Alto Networks", "ESET", "TrendMicro", 
                "BitDefender", "Sophos", "Avast", "AVG", "Malwarebytes", 
                "F-Secure", "Fortinet", "Check Point", "Cisco", "SentinelOne", 
                "Cybereason", "GData", "Comodo", "VIPRE", "Emsisoft", 
                "ZoneAlarm", "DrWeb", "Ikarus", "ClamAV", "Bkav", "TotalDefense"
            ],
            "trusted_vendors": [
                "Microsoft", "Google", "Kaspersky", "McAfee", "Symantec", 
                "CrowdStrike", "Palo Alto Networks"
            ],
            "vendor_categories": {
                "enterprise": ["Microsoft", "CrowdStrike", "Palo Alto Networks"],
                "consumer": ["Avast", "AVG", "McAfee"],
                "open_source": ["ClamAV"]
            }
        }
        self._save_json(self.vendors_file, vendors_config)
        logger.info(f"Created vendors configuration: {self.vendors_file}")
    
    def _save_json(self, file_path: Path, data: Dict[str, Any]) -> None:
        """Save data to JSON file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save {file_path}: {e}")
            raise
    
    def _load_json(self, file_path: Path) -> Dict[str, Any]:
        """Load data from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to load {file_path}: {e}")
            raise
    
    def get_api_keys(self) -> List[str]:
        """Get VirusTotal API keys from configuration"""
        try:
            config = self._load_json(self.api_keys_file)
            keys = config.get("virustotal_api_keys", [])
            
            # Filter out placeholder keys
            valid_keys = [key for key in keys if not key.startswith("YOUR_API_KEY")]
            
            if not valid_keys:
                raise ValueError("No valid API keys found. Please update the API keys configuration file.")
            
            logger.info(f"Loaded {len(valid_keys)} API keys")
            return valid_keys
            
        except Exception as e:
            logger.error(f"Failed to load API keys: {e}")
            raise
    
    def get_settings(self) -> Dict[str, Any]:
        """Get application settings"""
        try:
            settings = self._load_json(self.settings_file)
            # Merge with defaults to ensure all required settings exist
            merged_settings = {**self.default_settings, **settings}
            return merged_settings
        except Exception as e:
            logger.warning(f"Failed to load settings, using defaults: {e}")
            return self.default_settings.copy()
    
    def get_vendors(self) -> Dict[str, Any]:
        """Get vendor configuration"""
        try:
            return self._load_json(self.vendors_file)
        except Exception as e:
            logger.error(f"Failed to load vendors configuration: {e}")
            raise
    
    def update_setting(self, key: str, value: Any) -> None:
        """Update a specific setting"""
        try:
            settings = self.get_settings()
            settings[key] = value
            self._save_json(self.settings_file, settings)
            logger.info(f"Updated setting {key} = {value}")
        except Exception as e:
            logger.error(f"Failed to update setting {key}: {e}")
            raise
    
    def validate_configuration(self) -> bool:
        """Validate that all required configuration is present"""
        try:
            # Check API keys
            api_keys = self.get_api_keys()
            if not api_keys:
                logger.error("No valid API keys configured")
                return False
            
            # Check settings
            settings = self.get_settings()
            required_settings = ["max_workers", "api_delay", "request_timeout"]
            for setting in required_settings:
                if setting not in settings:
                    logger.error(f"Missing required setting: {setting}")
                    return False
            
            # Check vendors
            vendors = self.get_vendors()
            if not vendors.get("common_vendors"):
                logger.error("No vendors configured")
                return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

# Global configuration instance
config_manager = ConfigManager()

def get_config() -> ConfigManager:
    """Get the global configuration manager instance"""
    return config_manager
