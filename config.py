"""
Configuration management for IOC Analysis Tool
Handles secure loading of API keys and application settings using .env files
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False
    logger.warning("python-dotenv not installed. Install with: pip install python-dotenv")

class ConfigManager:
    """Secure configuration manager for IOC analysis tool with .env support"""

    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)

        # Configuration file paths
        self.env_file = Path(".env")
        self.env_example_file = Path(".env.example")
        self.api_keys_file = self.config_dir / "api_keys.json"
        self.settings_file = self.config_dir / "settings.json"
        self.vendors_file = self.config_dir / "vendors.json"

        # Load environment variables from .env file
        self._load_env_file()

        # Default settings
        self.default_settings = {
            "max_workers": 10,
            "api_delay": 2,
            "request_timeout": 30,
            "max_retries": 3,
            "output_format": "xlsx",
            "debug_mode": False
        }

        # Initialize configuration files if they don't exist
        self._initialize_config_files()

    def _load_env_file(self) -> None:
        """Load environment variables from .env file"""
        if DOTENV_AVAILABLE and self.env_file.exists():
            load_dotenv(self.env_file)
            logger.info(f"Loaded environment variables from {self.env_file}")
        elif not self.env_file.exists():
            # Create .env.example file for reference
            self._create_env_example()

    def _create_env_example(self) -> None:
        """Create .env.example file with template"""
        env_example_content = """# IOC Analysis Tool Environment Variables
# Copy this file to .env and fill in your actual values

# VirusTotal API Keys (you can add multiple keys separated by commas)
VIRUSTOTAL_API_KEYS=your_api_key_1,your_api_key_2,your_api_key_3

# Optional: Application Settings
# MAX_WORKERS=10
# API_DELAY=2
# REQUEST_TIMEOUT=30
# DEBUG_MODE=false

# Security Note:
# - Never commit .env files to version control
# - Add .env to your .gitignore file
# - Keep your API keys secure and private
"""

        try:
            with open(self.env_example_file, 'w', encoding='utf-8') as f:
                f.write(env_example_content)
            logger.info(f"Created .env.example template: {self.env_example_file}")
        except Exception as e:
            logger.error(f"Failed to create .env.example: {e}")

    def _initialize_config_files(self) -> None:
        """Initialize configuration files with default values"""

        # Initialize .env example if .env doesn't exist
        if not self.env_file.exists() and not self.env_example_file.exists():
            self._create_env_example()

        # Initialize API keys file (fallback for JSON method)
        if not self.api_keys_file.exists():
            self._create_api_keys_template()

        # Initialize settings file
        if not self.settings_file.exists():
            self._save_json(self.settings_file, self.default_settings)
            logger.info(f"Created default settings file: {self.settings_file}")

        # Initialize vendors file
        if not self.vendors_file.exists():
            self._create_vendors_file()
    
    def _create_api_keys_template(self) -> None:
        """Create API keys template file"""
        template = {
            "virustotal_api_keys": [
                "YOUR_API_KEY_1_HERE",
                "YOUR_API_KEY_2_HERE"
            ],
            "instructions": [
                "Replace the placeholder keys with your actual VirusTotal API keys",
                "You can add multiple keys for better rate limiting",
                "Never commit this file to version control",
                "Keep your API keys secure and private"
            ]
        }
        self._save_json(self.api_keys_file, template)
        logger.warning(f"Created API keys template: {self.api_keys_file}")
        logger.warning("Please update the API keys file with your actual keys!")
    
    def _create_vendors_file(self) -> None:
        """Create vendors configuration file"""
        vendors_config = {
            "common_vendors": [
                "Microsoft", "Google", "Kaspersky", "McAfee", "Symantec", 
                "CrowdStrike", "Palo Alto Networks", "ESET", "TrendMicro", 
                "BitDefender", "Sophos", "Avast", "AVG", "Malwarebytes", 
                "F-Secure", "Fortinet", "Check Point", "Cisco", "SentinelOne", 
                "Cybereason", "GData", "Comodo", "VIPRE", "Emsisoft", 
                "ZoneAlarm", "DrWeb", "Ikarus", "ClamAV", "Bkav", "TotalDefense"
            ],
            "trusted_vendors": [
                "Microsoft", "Google", "Kaspersky", "McAfee", "Symantec", 
                "CrowdStrike", "Palo Alto Networks"
            ],
            "vendor_categories": {
                "enterprise": ["Microsoft", "CrowdStrike", "Palo Alto Networks"],
                "consumer": ["Avast", "AVG", "McAfee"],
                "open_source": ["ClamAV"]
            }
        }
        self._save_json(self.vendors_file, vendors_config)
        logger.info(f"Created vendors configuration: {self.vendors_file}")
    
    def _save_json(self, file_path: Path, data: Dict[str, Any]) -> None:
        """Save data to JSON file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save {file_path}: {e}")
            raise
    
    def _load_json(self, file_path: Path) -> Dict[str, Any]:
        """Load data from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to load {file_path}: {e}")
            raise
    
    def get_api_keys(self) -> List[str]:
        """Get VirusTotal API keys from .env file or JSON configuration"""
        try:
            # First, try to get API keys from environment variables (.env file)
            env_keys = os.getenv("VIRUSTOTAL_API_KEYS")
            if env_keys:
                # Split comma-separated keys and clean them
                keys = [key.strip() for key in env_keys.split(",") if key.strip()]
                valid_keys = [key for key in keys if len(key) > 10]  # Basic validation

                if valid_keys:
                    logger.info(f"Loaded {len(valid_keys)} API keys from environment variables")
                    return valid_keys

            # Fallback to JSON configuration file
            if self.api_keys_file.exists():
                config = self._load_json(self.api_keys_file)
                keys = config.get("virustotal_api_keys", [])

                # Filter out placeholder keys
                valid_keys = [key for key in keys if not key.startswith("YOUR_API_KEY")]

                if valid_keys:
                    logger.info(f"Loaded {len(valid_keys)} API keys from JSON configuration")
                    return valid_keys

            # No valid keys found
            error_msg = (
                "No valid API keys found. Please configure your API keys using one of these methods:\n"
                "1. Create a .env file with: VIRUSTOTAL_API_KEYS=your_key_1,your_key_2\n"
                "2. Update config/api_keys.json with your actual keys\n"
                "3. Run: python setup_config.py"
            )
            raise ValueError(error_msg)

        except Exception as e:
            logger.error(f"Failed to load API keys: {e}")
            raise
    
    def get_settings(self) -> Dict[str, Any]:
        """Get application settings"""
        try:
            settings = self._load_json(self.settings_file)
            # Merge with defaults to ensure all required settings exist
            merged_settings = {**self.default_settings, **settings}
            return merged_settings
        except Exception as e:
            logger.warning(f"Failed to load settings, using defaults: {e}")
            return self.default_settings.copy()
    
    def get_vendors(self) -> Dict[str, Any]:
        """Get vendor configuration"""
        try:
            return self._load_json(self.vendors_file)
        except Exception as e:
            logger.error(f"Failed to load vendors configuration: {e}")
            raise
    
    def update_setting(self, key: str, value: Any) -> None:
        """Update a specific setting"""
        try:
            settings = self.get_settings()
            settings[key] = value
            self._save_json(self.settings_file, settings)
            logger.info(f"Updated setting {key} = {value}")
        except Exception as e:
            logger.error(f"Failed to update setting {key}: {e}")
            raise
    
    def validate_configuration(self) -> bool:
        """Validate that all required configuration is present"""
        try:
            # Check API keys
            api_keys = self.get_api_keys()
            if not api_keys:
                logger.error("No valid API keys configured")
                return False
            
            # Check settings
            settings = self.get_settings()
            required_settings = ["max_workers", "api_delay", "request_timeout"]
            for setting in required_settings:
                if setting not in settings:
                    logger.error(f"Missing required setting: {setting}")
                    return False
            
            # Check vendors
            vendors = self.get_vendors()
            if not vendors.get("common_vendors"):
                logger.error("No vendors configured")
                return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

# Global configuration instance
config_manager = ConfigManager()

def get_config() -> ConfigManager:
    """Get the global configuration manager instance"""
    return config_manager
