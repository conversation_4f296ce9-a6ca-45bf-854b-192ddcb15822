import pandas as pd
import requests
import time
from tkinter import Tk, filedialog
import base64
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from tqdm import tqdm

# List of API keys
api_keys = [
    'd70a0b180f31c230fbc27e0704a3d58ebae6eb3a504de183e8d522cf59b4ec3b',
    '0139d5f98e260b56d7ff3c96e276406de36a2e2430da683d6e92865531223d66',
    '08e131fb5f477e6c4333476e2b99f9a7ba7c586a3bad4c39a43a1756fd7c27ac',
    '78cb6e7581f918142e4ac3080d6112b1833c3736392e5f4d63792f78888edfca',
    'c44515ee67dcb60a84d1930905a8e55dc4eb70c1e49bb165cdf938d1f3f930f7',
    'cf651d39e830f9eb24817e9b4fda19c267dfa69106b847411602fbafc40d7d5d',
    '352bcd0eefc96877c5dc26b13a7449b1b85168fa307fe4c39dd394ac75450ab4',
]

# Class to manage API keys safely across threads
class APIKeyManager:
    def __init__(self, api_keys):  # Fixed typo from _init_ to __init__
        self.api_keys = api_keys.copy()
        self.lock = threading.Lock()
        self.index = 0  # Index of the next API key to use

    def get_api_key(self):
        with self.lock:
            if not self.api_keys:
                return None
            api_key = self.api_keys[self.index % len(self.api_keys)]
            self.index += 1
            return api_key

    def remove_api_key(self, api_key):
        with self.lock:
            if api_key in self.api_keys:
                self.api_keys.remove(api_key)
                if self.index >= len(self.api_keys):
                    self.index = 0

# Function to normalize IOCs
def normalize_ioc(ioc):
    ioc = ioc.replace('[.]', '.').replace('(.)', '.')
    ioc = ioc.replace('[://]', '://').replace('[:]', ':').replace('[', '').replace(']', '')
    ioc = ioc.replace('hxxp://', 'http://').replace('hxxps://', 'https://')
    return ioc.strip()

# Function to extract IOCs from Excel and categorize them
def extract_iocs_from_excel(excel_file):
    df = pd.read_excel(excel_file)
    iocs = {
        'URL': [],
        'FileHash-MD5': [],
        'FileHash-SHA1': [],
        'FileHash-SHA256': [],
        'domain': [],
        'hostname': [],
        'ip': []
    }
    for index, row in df.iterrows():
        if not pd.isna(row.iloc[0]):
            ioc_type = str(row.iloc[0]).lower()
            ioc_value = str(row.iloc[1]).replace('[.]', '.')
            if 'domain' in ioc_type:
                iocs['domain'].append(ioc_value)
            elif 'url' in ioc_type:
                iocs['URL'].append(ioc_value)
            elif 'md5' in ioc_type:
                iocs['FileHash-MD5'].append(ioc_value)
            elif 'sha1' in ioc_type:
                iocs['FileHash-SHA1'].append(ioc_value)
            elif 'sha256' in ioc_type:
                iocs['FileHash-SHA256'].append(ioc_value)
            elif 'hostname' in ioc_type:
                iocs['hostname'].append(ioc_value)
            elif 'ip' in ioc_type:
                iocs['ip'].append(ioc_value)
    return iocs

# Function to check IOCs using VirusTotal API with enhanced data
def check_ioc_virustotal(ioc, ioc_type, api_key_manager, delay=2):
    while True:
        api_key = api_key_manager.get_api_key()
        if api_key is None:
            return {"ioc": ioc, "result": "Error: All API keys exhausted", "malicious_count": 0, "app_names": []}

        headers = {"x-apikey": api_key}

        # Normalize and prepare URL based on IOC type
        if ioc_type == "URL":
            ioc_clean = normalize_ioc(ioc)
            ioc_encoded = base64.urlsafe_b64encode(ioc_clean.encode()).decode().strip("=")
            url = f"https://www.virustotal.com/api/v3/urls/{ioc_encoded}"
        elif ioc_type == "ip":
            ioc_clean = normalize_ioc(ioc)
            url = f"https://www.virustotal.com/api/v3/ip_addresses/{ioc_clean}"
        elif ioc_type in ["FileHash-MD5", "FileHash-SHA1", "FileHash-SHA256"]:
            ioc_clean = normalize_ioc(ioc)
            url = f"https://www.virustotal.com/api/v3/files/{ioc_clean}"
        elif ioc_type in ["domain", "hostname"]:
            ioc_clean = normalize_ioc(ioc)
            url = f"https://www.virustotal.com/api/v3/domains/{ioc_clean}"
        else:
            return {"ioc": ioc, "result": f"Unknown IOC type: {ioc_type}", "malicious_count": 0, "app_names": []}

        try:
            time.sleep(delay)
            tqdm.write(f"Using API key {api_key[:8]} for IOC: {ioc_clean}")
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if "data" in data and "attributes" in data["data"]:
                    attributes = data["data"]["attributes"]
                    vendors = attributes.get("last_analysis_results", {})
                    
                    # Count all vendors tagging as malicious
                    malicious_count = sum(1 for vendor in vendors.values() if vendor["category"] == "malicious")
                    
                    # Extract app names (for file hashes only, else N/A)
                    app_names = attributes.get("names", []) if ioc_type in ["FileHash-MD5", "FileHash-SHA1", "FileHash-SHA256"] else ["N/A"]
                    
                    # Determine result text
                    if malicious_count > 0:
                        result_text = f"Malicious (detected by {malicious_count} vendors)"
                    else:
                        result_text = "Not malicious or undetected"
                    
                    return {"ioc": ioc_clean, "result": result_text, "malicious_count": malicious_count, "app_names": app_names}
                else:
                    return {"ioc": ioc_clean, "result": "No data available", "malicious_count": 0, "app_names": ["N/A"]}

            elif response.status_code == 401:
                return {"ioc": ioc_clean, "result": "Unauthorized: Invalid API key", "malicious_count": 0, "app_names": ["N/A"]}
            elif response.status_code == 403 or response.status_code == 429:
                tqdm.write(f"API key quota exhausted for key {api_key[:8]}... Removing key.")
                api_key_manager.remove_api_key(api_key)
                continue
            elif response.status_code == 404:
                return {"ioc": ioc_clean, "result": "Not found in VirusTotal", "malicious_count": 0, "app_names": ["N/A"]}
            else:
                return {"ioc": ioc_clean, "result": f"Error {response.status_code}", "malicious_count": 0, "app_names": ["N/A"]}

        except Exception as e:
            tqdm.write(f"Error processing IOC {ioc_clean} with API key {api_key[:8]}: {e}")
            api_key_manager.remove_api_key(api_key)
            continue

# Function to process IOCs concurrently
def process_iocs_concurrently(iocs, api_keys):
    results = []
    total_iocs = sum(len(ioc_list) for ioc_list in iocs.values())
    api_key_manager = APIKeyManager(api_keys)

    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_ioc = {}
        for ioc_type, ioc_list in iocs.items():
            for ioc in ioc_list:
                future = executor.submit(check_ioc_virustotal, ioc, ioc_type, api_key_manager)
                future_to_ioc[future] = (ioc, ioc_type)

        with tqdm(total=total_iocs) as pbar:
            for future in as_completed(future_to_ioc):
                ioc, ioc_type = future_to_ioc[future]
                try:
                    result_dict = future.result()
                    results.append({
                        "Type": ioc_type,
                        "IOC": result_dict["ioc"],
                        "Result": result_dict["result"],
                        "Malicious_Vendors": result_dict["malicious_count"],
                        "App_Names": ", ".join(result_dict["app_names"])
                    })
                    pbar.update(1)
                except Exception as exc:
                    tqdm.write(f"IOC {ioc} generated an exception: {exc}")
                    pbar.update(1)

    return results

# Function to output results to Excel
def output_to_excel(results, output_file):
    df = pd.DataFrame(results)
    with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
        df.to_excel(writer, index=False, sheet_name="IOC Results")
    print(f"Results have been saved to {output_file}")

# Main function
def main():
    root = Tk()
    root.withdraw()

    excel_file = filedialog.askopenfilename(
        title="Select the Excel file with IOCs",
        filetypes=[("Excel Files", "*.xlsx *.xls")]
    )
    if not excel_file:
        print("No file selected, exiting.")
        return

    iocs = extract_iocs_from_excel(excel_file)
    results = process_iocs_concurrently(iocs, api_keys)

    output_file = filedialog.asksaveasfilename(
        title="Save Results As",
        defaultextension=".xlsx",
        filetypes=[("Excel Files", "*.xlsx *.xls")]
    )
    if not output_file:
        print("No output file selected, exiting.")
        return

    output_to_excel(results, output_file)

if __name__ == "__main__":  # Fixed typo from _main_ to __main__
    main()