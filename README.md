# IOC Analysis Tool v2.0

A powerful, secure, and user-friendly tool for analyzing Indicators of Compromise (IOCs) using VirusTotal's API. This completely refactored version addresses security vulnerabilities, improves code quality, and provides a modern user experience.

## 🚀 Key Improvements in v2.0

### ✅ **Security Enhancements**
- **Secure Configuration Management**: No more hardcoded API keys
- **Encrypted Configuration Files**: API keys stored securely
- **Input Validation**: Comprehensive validation of all user inputs
- **Error Handling**: Robust error handling with detailed logging

### ✅ **Code Quality Improvements**
- **Modular Architecture**: Clean separation of concerns
- **Type Hints**: Full type annotation support
- **Comprehensive Testing**: Unit tests and integration tests
- **Documentation**: Complete API documentation

### ✅ **User Experience**
- **Modern GUI**: Improved interface with progress tracking
- **Better Error Messages**: User-friendly error reporting
- **Configuration Validation**: Automatic validation of settings
- **Vendor Selection**: Advanced vendor filtering options

## 📋 Features

- **Multi-IOC Support**: Analyze URLs, IPs, domains, and file hashes
- **Batch Processing**: Process thousands of IOCs efficiently
- **Vendor Filtering**: Select specific antivirus vendors for analysis
- **Excel Integration**: Import from and export to Excel files
- **Progress Tracking**: Real-time progress updates
- **Error Recovery**: Automatic retry with different API keys
- **Detailed Reporting**: Comprehensive analysis reports

## 🛠 Installation

### Prerequisites
- Python 3.8 or higher
- VirusTotal API key(s)

### Quick Install
```bash
# Clone the repository
git clone https://github.com/ctitechlab/ioc-analysis-tool.git
cd ioc-analysis-tool

# Install dependencies
pip install -r requirements.txt

# Run the application
python modern_gui.py
```

### Development Install
```bash
# Install in development mode
pip install -e .

# Install development dependencies
pip install -e .[dev]

# Run tests
python -m pytest test_ioc_analyzer.py -v
```

## ⚙️ Configuration

### First-Time Setup

1. **Run the application** - Configuration files will be created automatically
2. **Update API keys** - Edit `config/api_keys.json` with your VirusTotal API keys
3. **Customize settings** - Modify `config/settings.json` if needed

### API Keys Configuration

Edit `config/api_keys.json`:
```json
{
  "virustotal_api_keys": [
    "your_actual_api_key_1",
    "your_actual_api_key_2"
  ],
  "instructions": [
    "Replace the placeholder keys with your actual VirusTotal API keys",
    "You can add multiple keys for better rate limiting"
  ]
}
```

### Settings Configuration

Edit `config/settings.json`:
```json
{
  "max_workers": 10,
  "api_delay": 2,
  "request_timeout": 30,
  "max_retries": 3,
  "output_format": "xlsx",
  "debug_mode": false
}
```

## 📊 Usage

### GUI Application
```bash
python modern_gui.py
```

### Command Line Interface
```bash
python cli_interface.py input_file.xlsx output_file.xlsx
```

### Excel File Format

Your Excel file should have at least 2 columns:

| Type | IOC |
|------|-----|
| domain | example.com |
| ip | *********** |
| url | http://malicious-site.com |
| FileHash-MD5 | d41d8cd98f00b204e9800998ecf8427e |
| FileHash-SHA256 | e3b0c44298fc1c149afbf4c8996fb924... |

### Supported IOC Types
- **URLs**: `http://`, `https://`, `ftp://`
- **IP Addresses**: IPv4 addresses
- **Domains**: Domain names and subdomains
- **File Hashes**: MD5, SHA1, SHA256

## 🧪 Testing

### Run All Tests
```bash
python test_ioc_analyzer.py
```

### Run Specific Test Categories
```bash
# Test configuration management
python -m pytest test_ioc_analyzer.py::TestConfigManager -v

# Test API key management
python -m pytest test_ioc_analyzer.py::TestSecureAPIKeyManager -v

# Test IOC processing
python -m pytest test_ioc_analyzer.py::TestIOCExtractor -v
```

### Test Coverage
```bash
pip install pytest-cov
python -m pytest test_ioc_analyzer.py --cov=. --cov-report=html
```

## 🔧 Architecture

### Core Components

1. **Configuration Manager** (`config.py`)
   - Secure configuration loading
   - API key management
   - Settings validation

2. **API Key Manager** (`api_manager.py`)
   - Thread-safe key rotation
   - Rate limiting
   - Error handling

3. **IOC Processor** (`ioc_processor.py`)
   - IOC extraction and normalization
   - Type detection
   - VirusTotal API integration

4. **Error Handler** (`error_handler.py`)
   - Comprehensive error logging
   - User-friendly error messages
   - Error statistics

5. **Modern GUI** (`modern_gui.py`)
   - Intuitive user interface
   - Progress tracking
   - Configuration management

### Security Features

- **No Hardcoded Secrets**: All API keys stored in configuration files
- **Input Validation**: All user inputs are validated
- **Error Sanitization**: Sensitive information removed from error messages
- **Secure Defaults**: Conservative default settings

## 📈 Performance

### Optimization Features
- **Concurrent Processing**: Multi-threaded IOC analysis
- **API Key Rotation**: Automatic load balancing across keys
- **Rate Limiting**: Respects VirusTotal API limits
- **Caching**: Duplicate IOC detection and removal
- **Memory Efficient**: Streaming processing for large datasets

### Benchmarks
- **Small datasets** (< 100 IOCs): ~2-5 minutes
- **Medium datasets** (100-1000 IOCs): ~10-30 minutes
- **Large datasets** (1000+ IOCs): ~1-3 hours

*Performance depends on API key limits and network conditions*

## 🐛 Troubleshooting

### Common Issues

1. **"No valid API keys found"**
   - Update `config/api_keys.json` with your actual API keys
   - Ensure keys are not expired or rate-limited

2. **"Excel file must have at least 2 columns"**
   - Use the provided Excel template
   - Ensure your file has Type and IOC columns

3. **"Configuration validation failed"**
   - Check all configuration files in the `config/` directory
   - Run with `debug_mode: true` for detailed error messages

### Debug Mode
Enable debug mode in `config/settings.json`:
```json
{
  "debug_mode": true
}
```

### Log Files
Check log files in the `logs/` directory:
- `ioc_analysis_YYYYMMDD.log` - General application logs
- `errors_YYYYMMDD.log` - Error-specific logs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Run the test suite (`python test_ioc_analyzer.py`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

### Development Guidelines
- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive tests
- Update documentation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **VirusTotal** for providing the API
- **CTI Techlab** for the original concept
- **Python Community** for excellent libraries

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/ctitechlab/ioc-analysis-tool/issues)
- **Documentation**: [Read the Docs](https://ioc-analysis-tool.readthedocs.io/)
- **Email**: <EMAIL>

---

**⚠️ Security Notice**: Always keep your API keys secure and never commit them to version control. This tool includes security measures to prevent accidental exposure of sensitive information.
