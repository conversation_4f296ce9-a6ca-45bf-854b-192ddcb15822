2025-07-02 16:36:11,675 - config - ERROR - config.py:139 - Failed to load API keys: No valid API keys found. Please update the API keys configuration file.
2025-07-02 16:36:11,773 - api_manager - ERROR - api_manager.py:81 - No active API keys available
2025-07-02 16:36:13,084 - error_handler - ERROR - error_handler.py:125 - Error occurred: DataProcessingError - Excel file must have at least 2 columns. Found 1
2025-07-02 16:36:13,084 - error_handler - ERROR - error_handler.py:130 - Context: {
  "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdvzlq2ry\\test_iocs.xlsx",
  "columns": [
    "IOC"
  ]
}
2025-07-02 16:44:58,022 - config - ERROR - config.py:139 - Failed to load API keys: No valid API keys found. Please update the API keys configuration file.
2025-07-02 16:44:58,105 - api_manager - ERROR - api_manager.py:81 - No active API keys available
2025-07-02 16:44:59,409 - error_handler - ERROR - error_handler.py:125 - Error occurred: DataProcessingError - Excel file must have at least 2 columns. Found 1
2025-07-02 16:44:59,409 - error_handler - ERROR - error_handler.py:130 - Context: {
  "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpj2oyx5e7\\test_iocs.xlsx",
  "columns": [
    "IOC"
  ]
}
2025-07-02 16:49:12,181 - config - ERROR - config.py:139 - Failed to load API keys: No valid API keys found. Please update the API keys configuration file.
2025-07-02 16:49:12,189 - config - ERROR - config.py:199 - Configuration validation failed: No valid API keys found. Please update the API keys configuration file.
