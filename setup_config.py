"""
Configuration Setup Helper for IOC Analysis Tool
Helps users configure their VirusTotal API keys and settings
"""

import json
import os
from pathlib import Path
from typing import List

def setup_api_keys():
    """Interactive setup for API keys"""
    print("🔑 VirusTotal API Key Setup")
    print("=" * 40)
    print()
    print("You need at least one VirusTotal API key to use this tool.")
    print("You can get a free API key from: https://www.virustotal.com/gui/my-apikey")
    print()
    
    api_keys = []
    
    while True:
        key = input("Enter a VirusTotal API key (or press Enter to finish): ").strip()
        
        if not key:
            if not api_keys:
                print("❌ You need at least one API key to continue.")
                continue
            else:
                break
        
        # Basic validation
        if len(key) != 64:
            print("⚠️  Warning: VirusTotal API keys are typically 64 characters long.")
            confirm = input("Continue anyway? (y/n): ").lower()
            if confirm != 'y':
                continue
        
        api_keys.append(key)
        print(f"✅ Added API key #{len(api_keys)}")
        
        if len(api_keys) >= 5:
            print("ℹ️  You have added 5 API keys. That should be sufficient for most use cases.")
            more = input("Add more keys? (y/n): ").lower()
            if more != 'y':
                break
    
    return api_keys

def save_api_keys(api_keys: List[str]):
    """Save API keys to configuration file"""
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    api_keys_file = config_dir / "api_keys.json"
    
    config_data = {
        "virustotal_api_keys": api_keys,
        "instructions": [
            "These are your VirusTotal API keys",
            "Keep them secure and never share them publicly",
            "You can add more keys by editing this file",
            "More keys = better rate limiting and performance"
        ]
    }
    
    with open(api_keys_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ API keys saved to: {api_keys_file}")

def setup_basic_settings():
    """Setup basic application settings"""
    print("\n⚙️  Basic Settings Configuration")
    print("=" * 40)
    
    settings = {
        "max_workers": 10,
        "api_delay": 2,
        "request_timeout": 30,
        "max_retries": 3,
        "output_format": "xlsx",
        "debug_mode": False
    }
    
    print("Current default settings:")
    for key, value in settings.items():
        print(f"  {key}: {value}")
    
    print("\nDo you want to customize these settings?")
    customize = input("(y/n, default: n): ").lower()
    
    if customize == 'y':
        print("\nCustomizing settings (press Enter to keep default):")
        
        # Max workers
        try:
            workers = input(f"Max concurrent workers ({settings['max_workers']}): ").strip()
            if workers:
                settings['max_workers'] = int(workers)
        except ValueError:
            print("Invalid number, keeping default")
        
        # API delay
        try:
            delay = input(f"API delay in seconds ({settings['api_delay']}): ").strip()
            if delay:
                settings['api_delay'] = float(delay)
        except ValueError:
            print("Invalid number, keeping default")
        
        # Debug mode
        debug = input(f"Enable debug mode? (y/n, default: n): ").lower()
        if debug == 'y':
            settings['debug_mode'] = True
    
    # Save settings
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    settings_file = config_dir / "settings.json"
    with open(settings_file, 'w', encoding='utf-8') as f:
        json.dump(settings, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Settings saved to: {settings_file}")

def check_existing_config():
    """Check if configuration already exists"""
    config_dir = Path("config")
    api_keys_file = config_dir / "api_keys.json"
    
    if api_keys_file.exists():
        try:
            with open(api_keys_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                keys = config.get("virustotal_api_keys", [])
                
                # Check if keys are real (not placeholders)
                valid_keys = [k for k in keys if not k.startswith("YOUR_API_KEY")]
                
                if valid_keys:
                    print(f"✅ Found existing configuration with {len(valid_keys)} API keys")
                    return True
                else:
                    print("⚠️  Found configuration file but no valid API keys")
                    return False
        except Exception as e:
            print(f"❌ Error reading existing configuration: {e}")
            return False
    
    return False

def validate_configuration():
    """Validate the current configuration"""
    try:
        from config import get_config
        config_manager = get_config()
        
        if config_manager.validate_configuration():
            print("✅ Configuration is valid!")
            
            # Show summary
            api_keys = config_manager.get_api_keys()
            settings = config_manager.get_settings()
            
            print(f"\nConfiguration Summary:")
            print(f"  API Keys: {len(api_keys)}")
            print(f"  Max Workers: {settings.get('max_workers', 'N/A')}")
            print(f"  API Delay: {settings.get('api_delay', 'N/A')}s")
            print(f"  Debug Mode: {settings.get('debug_mode', False)}")
            
            return True
        else:
            print("❌ Configuration validation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error validating configuration: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 IOC Analysis Tool - Configuration Setup")
    print("=" * 50)
    print()
    
    # Check existing configuration
    has_config = check_existing_config()
    
    if has_config:
        print("\nYou already have a valid configuration.")
        reconfigure = input("Do you want to reconfigure? (y/n): ").lower()
        if reconfigure != 'y':
            print("Configuration setup cancelled.")
            return
    
    try:
        # Setup API keys
        api_keys = setup_api_keys()
        save_api_keys(api_keys)
        
        # Setup basic settings
        setup_basic_settings()
        
        print("\n🎉 Configuration setup complete!")
        print("=" * 50)
        
        # Validate configuration
        print("\nValidating configuration...")
        if validate_configuration():
            print("\n✅ You're all set! You can now run the IOC Analysis Tool:")
            print("   GUI: python modern_gui.py")
            print("   CLI: python cli_interface.py input.xlsx output.xlsx")
        else:
            print("\n❌ Configuration validation failed. Please check your settings.")
    
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")

if __name__ == "__main__":
    main()
