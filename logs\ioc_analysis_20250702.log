2025-07-02 16:36:11,155 - error_handler - INFO - error_handler.py:98 - Error handling system initialized
2025-07-02 16:36:11,662 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmpky82h_6b\api_keys.json
2025-07-02 16:36:11,662 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:36:11,666 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmpky82h_6b\settings.json
2025-07-02 16:36:11,668 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmpky82h_6b\vendors.json
2025-07-02 16:36:11,675 - config - ERROR - config.py:139 - Failed to load API keys: No valid API keys found. Please update the API keys configuration file.
2025-07-02 16:36:11,686 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmpoja8ufc0\api_keys.json
2025-07-02 16:36:11,686 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:36:11,688 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmpoja8ufc0\settings.json
2025-07-02 16:36:11,694 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmpoja8ufc0\vendors.json
2025-07-02 16:36:11,704 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmpob2eh4_7\api_keys.json
2025-07-02 16:36:11,704 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:36:11,706 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmpob2eh4_7\settings.json
2025-07-02 16:36:11,706 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmpob2eh4_7\vendors.json
2025-07-02 16:36:11,727 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmpkkzbnz_u\api_keys.json
2025-07-02 16:36:11,727 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:36:11,732 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmpkkzbnz_u\settings.json
2025-07-02 16:36:11,734 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmpkkzbnz_u\vendors.json
2025-07-02 16:36:11,756 - config - INFO - config.py:135 - Loaded 2 API keys
2025-07-02 16:36:11,761 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmp8fagr6f_\api_keys.json
2025-07-02 16:36:11,763 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:36:11,765 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmp8fagr6f_\settings.json
2025-07-02 16:36:11,766 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmp8fagr6f_\vendors.json
2025-07-02 16:36:11,772 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:36:11,772 - api_manager - WARNING - api_manager.py:142 - Marked API key as exhausted: Test
2025-07-02 16:36:11,773 - api_manager - WARNING - api_manager.py:142 - Marked API key as exhausted: Test
2025-07-02 16:36:11,773 - api_manager - WARNING - api_manager.py:142 - Marked API key as exhausted: Test
2025-07-02 16:36:11,773 - api_manager - ERROR - api_manager.py:81 - No active API keys available
2025-07-02 16:36:11,774 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:36:11,776 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:36:11,778 - api_manager - WARNING - api_manager.py:110 - All API keys are rate limited, waiting...
2025-07-02 16:36:11,781 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:36:11,781 - api_manager - WARNING - api_manager.py:110 - All API keys are rate limited, waiting...
2025-07-02 16:36:11,784 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:36:11,784 - api_manager - WARNING - api_manager.py:110 - All API keys are rate limited, waiting...
2025-07-02 16:36:11,784 - api_manager - WARNING - api_manager.py:110 - All API keys are rate limited, waiting...
2025-07-02 16:36:11,784 - api_manager - WARNING - api_manager.py:110 - All API keys are rate limited, waiting...
2025-07-02 16:36:11,785 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:36:11,785 - api_manager - WARNING - api_manager.py:110 - All API keys are rate limited, waiting...
2025-07-02 16:36:11,785 - api_manager - WARNING - api_manager.py:110 - All API keys are rate limited, waiting...
2025-07-02 16:36:13,084 - error_handler - ERROR - error_handler.py:125 - Error occurred: DataProcessingError - Excel file must have at least 2 columns. Found 1
2025-07-02 16:36:13,084 - error_handler - ERROR - error_handler.py:130 - Context: {
  "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdvzlq2ry\\test_iocs.xlsx",
  "columns": [
    "IOC"
  ]
}
2025-07-02 16:44:57,729 - error_handler - INFO - error_handler.py:98 - Error handling system initialized
2025-07-02 16:44:57,989 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmp91faxr18\api_keys.json
2025-07-02 16:44:57,989 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:44:57,989 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmp91faxr18\settings.json
2025-07-02 16:44:58,022 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmp91faxr18\vendors.json
2025-07-02 16:44:58,022 - config - ERROR - config.py:139 - Failed to load API keys: No valid API keys found. Please update the API keys configuration file.
2025-07-02 16:44:58,032 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmpaplg1y4i\api_keys.json
2025-07-02 16:44:58,032 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:44:58,032 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmpaplg1y4i\settings.json
2025-07-02 16:44:58,038 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmpaplg1y4i\vendors.json
2025-07-02 16:44:58,045 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmp1_72qi7z\api_keys.json
2025-07-02 16:44:58,045 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:44:58,045 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmp1_72qi7z\settings.json
2025-07-02 16:44:58,054 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmp1_72qi7z\vendors.json
2025-07-02 16:44:58,063 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmpkb91lwv6\api_keys.json
2025-07-02 16:44:58,063 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:44:58,063 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmpkb91lwv6\settings.json
2025-07-02 16:44:58,077 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmpkb91lwv6\vendors.json
2025-07-02 16:44:58,081 - config - INFO - config.py:135 - Loaded 2 API keys
2025-07-02 16:44:58,081 - config - WARNING - config.py:72 - Created API keys template: C:\Users\<USER>\AppData\Local\Temp\tmpo74g2jh1\api_keys.json
2025-07-02 16:44:58,081 - config - WARNING - config.py:73 - Please update the API keys file with your actual keys!
2025-07-02 16:44:58,087 - config - INFO - config.py:51 - Created default settings file: C:\Users\<USER>\AppData\Local\Temp\tmpo74g2jh1\settings.json
2025-07-02 16:44:58,095 - config - INFO - config.py:97 - Created vendors configuration: C:\Users\<USER>\AppData\Local\Temp\tmpo74g2jh1\vendors.json
2025-07-02 16:44:58,095 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:44:58,095 - api_manager - WARNING - api_manager.py:142 - Marked API key as exhausted: Test
2025-07-02 16:44:58,095 - api_manager - WARNING - api_manager.py:142 - Marked API key as exhausted: Test
2025-07-02 16:44:58,103 - api_manager - WARNING - api_manager.py:142 - Marked API key as exhausted: Test
2025-07-02 16:44:58,105 - api_manager - ERROR - api_manager.py:81 - No active API keys available
2025-07-02 16:44:58,106 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:44:58,106 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:44:58,222 - api_manager - WARNING - api_manager.py:142 - Marked API key as exhausted: Test exhaustion
2025-07-02 16:44:58,222 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:44:58,335 - api_manager - WARNING - api_manager.py:126 - Marked API key as invalid: Test invalidation
2025-07-02 16:44:58,336 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:44:58,559 - api_manager - INFO - api_manager.py:66 - Initialized API key manager with 3 keys
2025-07-02 16:44:59,409 - error_handler - ERROR - error_handler.py:125 - Error occurred: DataProcessingError - Excel file must have at least 2 columns. Found 1
2025-07-02 16:44:59,409 - error_handler - ERROR - error_handler.py:130 - Context: {
  "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpj2oyx5e7\\test_iocs.xlsx",
  "columns": [
    "IOC"
  ]
}
2025-07-02 16:47:05,638 - error_handler - INFO - error_handler.py:98 - Error handling system initialized
2025-07-02 16:49:11,896 - error_handler - INFO - error_handler.py:98 - Error handling system initialized
2025-07-02 16:49:12,181 - config - ERROR - config.py:139 - Failed to load API keys: No valid API keys found. Please update the API keys configuration file.
2025-07-02 16:49:12,189 - config - ERROR - config.py:199 - Configuration validation failed: No valid API keys found. Please update the API keys configuration file.
2025-07-02 17:01:10,200 - error_handler - INFO - error_handler.py:98 - Error handling system initialized
2025-07-02 17:01:10,588 - config - ERROR - config.py:211 - Failed to load API keys: No valid API keys found. Please configure your API keys using one of these methods:
1. Create a .env file with: VIRUSTOTAL_API_KEYS=your_key_1,your_key_2
2. Update config/api_keys.json with your actual keys
3. Run: python setup_config.py
2025-07-02 17:01:10,592 - config - ERROR - config.py:271 - Configuration validation failed: No valid API keys found. Please configure your API keys using one of these methods:
1. Create a .env file with: VIRUSTOTAL_API_KEYS=your_key_1,your_key_2
2. Update config/api_keys.json with your actual keys
3. Run: python setup_config.py
2025-07-02 17:05:27,427 - error_handler - INFO - error_handler.py:98 - Error handling system initialized
2025-07-02 17:05:27,780 - config - INFO - config.py:186 - Loaded 7 API keys from environment variables
2025-07-02 17:05:27,785 - config - INFO - config.py:267 - Configuration validation passed
