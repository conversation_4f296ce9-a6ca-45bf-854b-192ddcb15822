"""
Modern GUI Interface for IOC Analysis Tool
Improved user experience with better error handling and progress feedback
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from typing import Optional, List, Callable, Dict, Any
import logging
from pathlib import Path

from config import get_config
from error_handler import get_error_handler, UserFriendlyErrorReporter

logger = logging.getLogger(__name__)

class ProgressDialog:
    """Modern progress dialog with cancellation support"""
    
    def __init__(self, parent: tk.Tk, title: str = "Processing", message: str = "Please wait..."):
        self.parent = parent
        self.cancelled = False
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self._center_dialog()
        
        # Create widgets
        self._create_widgets(message)
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.cancel)
    
    def _center_dialog(self):
        """Center dialog relative to parent"""
        self.dialog.update_idletasks()
        x = (self.parent.winfo_x() + (self.parent.winfo_width() // 2) - 
             (self.dialog.winfo_width() // 2))
        y = (self.parent.winfo_y() + (self.parent.winfo_height() // 2) - 
             (self.dialog.winfo_height() // 2))
        self.dialog.geometry(f"+{x}+{y}")
    
    def _create_widgets(self, message: str):
        """Create dialog widgets"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Message label
        self.message_label = ttk.Label(main_frame, text=message, font=("Segoe UI", 10))
        self.message_label.pack(pady=(0, 10))
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            mode='indeterminate', 
            length=300
        )
        self.progress_bar.pack(pady=(0, 10))
        self.progress_bar.start(10)
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="Initializing...", font=("Segoe UI", 9))
        self.status_label.pack(pady=(0, 10))
        
        # Cancel button
        self.cancel_button = ttk.Button(main_frame, text="Cancel", command=self.cancel)
        self.cancel_button.pack()
    
    def update_message(self, message: str):
        """Update the main message"""
        if not self.cancelled:
            self.message_label.config(text=message)
            self.dialog.update()
    
    def update_status(self, status: str):
        """Update the status text"""
        if not self.cancelled:
            self.status_label.config(text=status)
            self.dialog.update()
    
    def set_progress(self, value: int, maximum: int = 100):
        """Set progress bar to determinate mode with specific value"""
        if not self.cancelled:
            self.progress_bar.config(mode='determinate', maximum=maximum, value=value)
            self.dialog.update()
    
    def cancel(self):
        """Cancel the operation"""
        self.cancelled = True
        self.dialog.destroy()
    
    def close(self):
        """Close the dialog"""
        if not self.cancelled:
            self.progress_bar.stop()
            self.dialog.destroy()

class VendorSelectionDialog:
    """Modern vendor selection dialog"""
    
    def __init__(self, parent: tk.Tk, vendors: List[str]):
        self.parent = parent
        self.vendors = vendors
        self.selected_vendors = None
        self.result = None
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Select VirusTotal Vendors")
        self.dialog.geometry("500x600")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self._center_dialog()
        
        # Create widgets
        self._create_widgets()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
    
    def _center_dialog(self):
        """Center dialog relative to parent"""
        self.dialog.update_idletasks()
        x = (self.parent.winfo_x() + (self.parent.winfo_width() // 2) - 
             (self.dialog.winfo_width() // 2))
        y = (self.parent.winfo_y() + (self.parent.winfo_height() // 2) - 
             (self.dialog.winfo_height() // 2))
        self.dialog.geometry(f"+{x}+{y}")
    
    def _create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="Select VirusTotal Vendors", 
            font=("Segoe UI", 12, "bold")
        )
        title_label.pack(pady=(0, 10))
        
        # Description
        desc_label = ttk.Label(
            main_frame,
            text="Choose which antivirus vendors to include in the analysis:",
            font=("Segoe UI", 9)
        )
        desc_label.pack(pady=(0, 10))
        
        # All vendors option
        self.all_vendors_var = tk.BooleanVar(value=True)
        all_frame = ttk.LabelFrame(main_frame, text="Quick Selection", padding="10")
        all_frame.pack(fill=tk.X, pady=(0, 10))
        
        all_checkbox = ttk.Checkbutton(
            all_frame,
            text="Use All Vendors (Recommended)",
            variable=self.all_vendors_var,
            command=self._toggle_vendor_list
        )
        all_checkbox.pack(anchor=tk.W)
        
        # Vendor list frame
        list_frame = ttk.LabelFrame(main_frame, text="Specific Vendors", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Search frame
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self._filter_vendors)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # Vendor listbox with scrollbar
        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(listbox_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.vendor_listbox = tk.Listbox(
            listbox_frame,
            selectmode=tk.MULTIPLE,
            yscrollcommand=scrollbar.set,
            font=("Segoe UI", 9)
        )
        self.vendor_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.vendor_listbox.yview)
        
        # Populate listbox
        self._populate_vendor_list()
        
        # Selection buttons
        selection_frame = ttk.Frame(list_frame)
        selection_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(selection_frame, text="Select All", command=self._select_all).pack(side=tk.LEFT)
        ttk.Button(selection_frame, text="Clear All", command=self._clear_all).pack(side=tk.LEFT, padx=(5, 0))
        
        # Action buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="OK", command=self._on_ok).pack(side=tk.RIGHT, padx=(0, 5))
        
        # Initial state
        self._toggle_vendor_list()
    
    def _populate_vendor_list(self, filter_text: str = ""):
        """Populate vendor listbox with optional filtering"""
        self.vendor_listbox.delete(0, tk.END)
        
        filtered_vendors = [
            vendor for vendor in self.vendors
            if filter_text.lower() in vendor.lower()
        ]
        
        for vendor in sorted(filtered_vendors):
            self.vendor_listbox.insert(tk.END, vendor)
    
    def _filter_vendors(self, *args):
        """Filter vendors based on search text"""
        filter_text = self.search_var.get()
        self._populate_vendor_list(filter_text)
    
    def _toggle_vendor_list(self):
        """Toggle vendor list based on 'All' checkbox"""
        if self.all_vendors_var.get():
            self.vendor_listbox.config(state=tk.DISABLED)
            self.search_var.set("")
        else:
            self.vendor_listbox.config(state=tk.NORMAL)
    
    def _select_all(self):
        """Select all vendors in the list"""
        self.vendor_listbox.select_set(0, tk.END)
    
    def _clear_all(self):
        """Clear all vendor selections"""
        self.vendor_listbox.selection_clear(0, tk.END)
    
    def _on_ok(self):
        """Handle OK button click"""
        if self.all_vendors_var.get():
            self.result = "All"
        else:
            selected_indices = self.vendor_listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("No Selection", "Please select at least one vendor or choose 'Use All Vendors'.")
                return
            
            self.result = [self.vendor_listbox.get(i) for i in selected_indices]
        
        self.dialog.destroy()
    
    def _on_cancel(self):
        """Handle Cancel button click"""
        self.result = None
        self.dialog.destroy()
    
    def show(self) -> Optional[List[str]]:
        """Show dialog and return result"""
        self.dialog.wait_window()
        return self.result

class ModernIOCAnalyzerGUI:
    """Modern GUI for IOC Analysis Tool"""
    
    def __init__(self):
        self.config = get_config()
        self.error_handler = get_error_handler()
        self.error_reporter = UserFriendlyErrorReporter()
        
        # Initialize main window
        self.root = tk.Tk()
        self.root.title("IOC Analysis Tool - CTI Techlab")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Configure style
        self._configure_style()
        
        # Create GUI
        self._create_widgets()
        
        # Center window
        self._center_window()
    
    def _configure_style(self):
        """Configure modern styling"""
        style = ttk.Style()
        
        # Try to use a modern theme
        available_themes = style.theme_names()
        if 'clam' in available_themes:
            style.theme_use('clam')
        elif 'vista' in available_themes:
            style.theme_use('vista')
        
        # Configure custom styles
        style.configure('Title.TLabel', font=('Segoe UI', 16, 'bold'))
        style.configure('Subtitle.TLabel', font=('Segoe UI', 10))
        style.configure('Header.TLabel', font=('Segoe UI', 12, 'bold'))
    
    def _center_window(self):
        """Center the main window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """Create main GUI widgets"""
        # Main container
        main_container = ttk.Frame(self.root, padding="20")
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # Header
        header_frame = ttk.Frame(main_container)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(
            header_frame,
            text="IOC Analysis Tool",
            style='Title.TLabel'
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            header_frame,
            text="Analyze Indicators of Compromise using VirusTotal",
            style='Subtitle.TLabel'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # File selection section
        file_section = ttk.LabelFrame(main_container, text="Input File", padding="15")
        file_section.pack(fill=tk.X, pady=(0, 15))
        
        self.file_path_var = tk.StringVar()
        file_frame = ttk.Frame(file_section)
        file_frame.pack(fill=tk.X)
        
        ttk.Label(file_frame, text="Excel File:").pack(side=tk.LEFT)
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state='readonly')
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        
        ttk.Button(file_frame, text="Browse", command=self._browse_input_file).pack(side=tk.RIGHT)
        
        # Configuration section
        config_section = ttk.LabelFrame(main_container, text="Configuration", padding="15")
        config_section.pack(fill=tk.X, pady=(0, 15))
        
        # Vendor selection
        vendor_frame = ttk.Frame(config_section)
        vendor_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(vendor_frame, text="Vendors:").pack(side=tk.LEFT)
        self.vendor_selection_var = tk.StringVar(value="All Vendors")
        vendor_label = ttk.Label(vendor_frame, textvariable=self.vendor_selection_var, relief=tk.SUNKEN)
        vendor_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        
        ttk.Button(vendor_frame, text="Select", command=self._select_vendors).pack(side=tk.RIGHT)
        
        # Output section
        output_section = ttk.LabelFrame(main_container, text="Output", padding="15")
        output_section.pack(fill=tk.X, pady=(0, 15))
        
        self.output_path_var = tk.StringVar()
        output_frame = ttk.Frame(output_section)
        output_frame.pack(fill=tk.X)
        
        ttk.Label(output_frame, text="Save As:").pack(side=tk.LEFT)
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, state='readonly')
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        
        ttk.Button(output_frame, text="Browse", command=self._browse_output_file).pack(side=tk.RIGHT)
        
        # Action buttons
        action_frame = ttk.Frame(main_container)
        action_frame.pack(fill=tk.X, pady=(20, 0))
        
        self.analyze_button = ttk.Button(
            action_frame,
            text="Start Analysis",
            command=self._start_analysis,
            style='Accent.TButton'
        )
        self.analyze_button.pack(side=tk.RIGHT)
        
        ttk.Button(action_frame, text="Exit", command=self.root.quit).pack(side=tk.RIGHT, padx=(0, 10))
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_container, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
        # Store selected vendors
        self.selected_vendors = "All"
    
    def _browse_input_file(self):
        """Browse for input Excel file"""
        file_path = filedialog.askopenfilename(
            title="Select Excel file with IOCs",
            filetypes=[("Excel Files", "*.xlsx *.xls"), ("All Files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
            self.status_var.set(f"Selected input file: {Path(file_path).name}")
    
    def _browse_output_file(self):
        """Browse for output file location"""
        file_path = filedialog.asksaveasfilename(
            title="Save results as",
            defaultextension=".xlsx",
            filetypes=[("Excel Files", "*.xlsx"), ("All Files", "*.*")]
        )
        if file_path:
            self.output_path_var.set(file_path)
            self.status_var.set(f"Output will be saved to: {Path(file_path).name}")
    
    def _select_vendors(self):
        """Show vendor selection dialog"""
        try:
            vendors_config = self.config.get_vendors()
            common_vendors = vendors_config.get("common_vendors", [])
            
            dialog = VendorSelectionDialog(self.root, common_vendors)
            result = dialog.show()
            
            if result is not None:
                self.selected_vendors = result
                if result == "All":
                    self.vendor_selection_var.set("All Vendors")
                else:
                    vendor_count = len(result)
                    self.vendor_selection_var.set(f"{vendor_count} vendors selected")
                
                self.status_var.set("Vendor selection updated")
        
        except Exception as e:
            error_msg = self.error_reporter.get_user_message(e)
            messagebox.showerror("Configuration Error", error_msg)
            self.error_handler.handle_error(e, {"action": "vendor_selection"})
    
    def _start_analysis(self):
        """Start IOC analysis in background thread"""
        # Validate inputs
        if not self.file_path_var.get():
            messagebox.showwarning("Missing Input", "Please select an input Excel file.")
            return

        if not self.output_path_var.get():
            messagebox.showwarning("Missing Output", "Please specify an output file location.")
            return

        # Check if configuration is valid before starting analysis
        if not self.config.validate_configuration():
            messagebox.showerror(
                "Configuration Required",
                "Please configure your VirusTotal API keys before starting analysis:\n\n"
                "1. Edit 'config/api_keys.json'\n"
                "2. Replace placeholder keys with your actual VirusTotal API keys\n"
                "3. Restart the application\n\n"
                "You can get API keys from: https://www.virustotal.com/gui/my-apikey"
            )
            return

        # Disable analyze button
        self.analyze_button.config(state=tk.DISABLED)

        # Start analysis in background thread
        analysis_thread = threading.Thread(
            target=self._run_analysis,
            args=(self.file_path_var.get(), self.output_path_var.get(), self.selected_vendors),
            daemon=True
        )
        analysis_thread.start()
    
    def _run_analysis(self, input_file: str, output_file: str, selected_vendors):
        """Run analysis in background thread"""
        progress_dialog = None
        
        try:
            # Show progress dialog
            progress_dialog = ProgressDialog(self.root, "IOC Analysis", "Analyzing IOCs...")
            progress_dialog.update_status("Initializing analysis...")
            
            # Import analysis modules here to avoid circular imports
            from ioc_processor import IOCExtractor, VirusTotalClient
            from api_manager import SecureAPIKeyManager
            
            # Initialize components
            api_keys = self.config.get_api_keys()
            api_manager = SecureAPIKeyManager(api_keys)
            extractor = IOCExtractor()
            vt_client = VirusTotalClient(api_manager)
            
            # Extract IOCs
            progress_dialog.update_status("Extracting IOCs from Excel file...")
            iocs, total_read, duplicates_skipped = extractor.extract_from_excel(input_file)
            
            if progress_dialog.cancelled:
                return
            
            # Analyze IOCs
            progress_dialog.update_status("Analyzing IOCs with VirusTotal...")
            # TODO: Implement concurrent analysis with progress updates
            
            # Save results
            progress_dialog.update_status("Saving results...")
            # TODO: Implement result saving
            
            # Close progress dialog
            progress_dialog.close()
            
            # Show success message
            self.root.after(0, lambda: messagebox.showinfo(
                "Analysis Complete",
                f"Analysis completed successfully!\nResults saved to: {Path(output_file).name}"
            ))
            
        except Exception as e:
            if progress_dialog:
                progress_dialog.close()
            
            error_msg = self.error_reporter.get_user_message(e)
            self.root.after(0, lambda: messagebox.showerror("Analysis Error", error_msg))
            self.error_handler.handle_error(e, {"input_file": input_file, "output_file": output_file})
        
        finally:
            # Re-enable analyze button
            self.root.after(0, lambda: self.analyze_button.config(state=tk.NORMAL))
    
    def run(self):
        """Run the GUI application"""
        try:
            # Check if configuration is valid, but don't block startup
            config_valid = self.config.validate_configuration()

            if not config_valid:
                # Show helpful message about configuration
                messagebox.showwarning(
                    "Configuration Setup Required",
                    "Welcome to IOC Analysis Tool v2.0!\n\n"
                    "To get started, you need to configure your VirusTotal API keys:\n\n"
                    "1. The configuration files have been created in the 'config' folder\n"
                    "2. Edit 'config/api_keys.json' and replace the placeholder keys with your actual VirusTotal API keys\n"
                    "3. Restart the application\n\n"
                    "You can still use the application to browse files and configure settings."
                )
                self.status_var.set("⚠️ Configuration required - Please update API keys in config/api_keys.json")
            else:
                self.status_var.set("✅ Ready - Select an Excel file to begin analysis")

            self.root.mainloop()

        except Exception as e:
            error_msg = self.error_reporter.get_user_message(e)
            messagebox.showerror("Application Error", error_msg)
            self.error_handler.handle_error(e, {"action": "gui_startup"})

def main():
    """Main entry point for GUI application"""
    app = ModernIOCAnalyzerGUI()
    app.run()

if __name__ == "__main__":
    main()
