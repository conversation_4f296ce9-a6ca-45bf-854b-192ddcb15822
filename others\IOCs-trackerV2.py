import pandas as pd
import requests
import time
from tkinter import Tk, filedialog
import base64
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from tqdm import tqdm
import re
import datetime
import random
from time import sleep

# List of API keys for VirusTotal
api_keys = [
    '****************************************************************',
    '0139d5f98e260b56d7ff3c96e276406de36a2e2430da683d6e92865531223d66',
    '08e131fb5f477e6c4333476e2b99f9a7ba7c586a3bad4c39a43a1756fd7c27ac',
    '78cb6e7581f918142e4ac3080d6112b1833c3736392e5f4d63792f78888edfca',
    'c44515ee67dcb60a84d1930905a8e55dc4eb70c1e49bb165cdf938d1f3f930f7',
    'cf651d39e830f9eb24817e9b4fda19c267dfa69106b847411602fbafc40d7d5d',
    '352bcd0eefc96877c5dc26b13a7449b1b85168fa307fe4c39dd394ac75450ab4',
]

# Banner ASCII
banner = """
 ██████╗████████╗██╗    ████████╗███████╗ ██████╗██╗  ██╗██╗      █████╗ ██████╗ 
██╔════╝╚══██╔══╝██║    ╚══██╔══╝██╔════╝██╔════╝██║  ██║██║     ██╔══██╗██╔══██╗
██║        ██║   ██║       ██║   █████╗  ██║     ███████║██║     ███████║██████╔╝
██║        ██║   ██║       ██║   ██╔══╝  ██║     ██╔══██║██║     ██╔══██║██╔══██╗
╚██████╗   ██║   ██║       ██║   ███████╗╚██████╗██║  ██║███████╗██║  ██║██████╔╝
 ╚═════╝   ╚═╝   ╚═╝       ╚═╝   ╚══════╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═════╝ 
"""

# Description
description = """
Alat ini menggunakan VirusTotal untuk menganalisis Petunjuk Kompromi (IOCs).
Ia akan mengambil maklumat seperti bilangan vendor malicious, nama aplikasi,
enjin yang mengesan, dan tarikh analisis terakhir dari VirusTotal.
Sila sediakan fail Excel dengan dua lajur: jenis IOC (contoh: URL, ip, FileHash-SHA256)
dan nilai IOC.
"""

# Common software name mappings for standardization
COMMON_SOFTWARE_MAPPINGS = {
    # Microsoft Products
    r'msedge': 'Microsoft Edge',
    r'iexplore': 'Internet Explorer',
    r'winword': 'Microsoft Word',
    r'excel': 'Microsoft Excel',
    r'powerpnt': 'Microsoft PowerPoint',
    r'outlook': 'Microsoft Outlook',
    r'msteams': 'Microsoft Teams',
    r'msoffice': 'Microsoft Office',
    
    # Adobe Products
    r'acrord32': 'Adobe Reader',
    r'acrobat': 'Adobe Acrobat',
    r'photoshop': 'Adobe Photoshop',
    r'illustrator': 'Adobe Illustrator',
    
    # Browsers
    r'chrome': 'Google Chrome',
    r'firefox': 'Mozilla Firefox',
    r'opera': 'Opera Browser',
    
    # Common Software
    r'notepad\+\+': 'Notepad++',
    r'winrar': 'WinRAR',
    r'7z': '7-Zip',
    r'vlc': 'VLC Media Player',
    r'zoom': 'Zoom Client',
    r'slack': 'Slack',
    r'vscode': 'Visual Studio Code',
    r'putty': 'PuTTY',
    r'winscp': 'WinSCP',
}

# Class to manage API keys safely across threads
class APIKeyManager:
    def __init__(self, api_keys):
        self.lock = threading.Lock()
        # Initialize API key states with more efficient tracking
        self.api_keys = []
        for key in api_keys:
            self.api_keys.append({
                'key': key,
                'requests_remaining': 4,  # VT allows 4 requests per minute
                'last_reset': datetime.datetime.now(),
                'last_used': datetime.datetime.now() - datetime.timedelta(minutes=1),
                'is_active': True
            })
        self.current_index = 0
        
    def get_api_key(self):
        with self.lock:
            start_index = self.current_index
            now = datetime.datetime.now()
            
            # Try each key once
            while True:
                key_info = self.api_keys[self.current_index]
                
                # Check if key needs reset (1 minute passed since last reset)
                if key_info['is_active'] and (now - key_info['last_reset']).total_seconds() >= 60:
                    key_info['requests_remaining'] = 4
                    key_info['last_reset'] = now
                
                # If key has requests remaining and is active
                if key_info['requests_remaining'] > 0 and key_info['is_active']:
                    # Calculate minimum wait time needed
                    wait_time = 0
                    if (now - key_info['last_used']).total_seconds() < 15:  # Minimum 15 seconds between requests
                        wait_time = 15 - (now - key_info['last_used']).total_seconds()
                    
                    if wait_time > 0:
                        sleep(wait_time)
                    
                    key_info['requests_remaining'] -= 1
                    key_info['last_used'] = datetime.datetime.now()
                    
                    # Rotate to next key
                    self.current_index = (self.current_index + 1) % len(self.api_keys)
                    return key_info['key']
                
                # Try next key
                self.current_index = (self.current_index + 1) % len(self.api_keys)
                
                # If we've tried all keys, check if any will reset soon
                if self.current_index == start_index:
                    min_wait = float('inf')
                    for key in self.api_keys:
                        if key['is_active']:
                            time_until_reset = 60 - (now - key['last_reset']).total_seconds()
                            if time_until_reset > 0:
                                min_wait = min(min_wait, time_until_reset)
                    
                    if min_wait < float('inf'):
                        sleep(min_wait + 1)  # Add 1 second buffer
                        continue
                    return None
    
    def remove_api_key(self, api_key):
        with self.lock:
            for key_info in self.api_keys:
                if key_info['key'] == api_key:
                    key_info['is_active'] = False
                    break

# Function to normalize IOCs
def normalize_ioc(ioc):
    ioc = ioc.replace('[.]', '.').replace('(.)', '.')
    ioc = ioc.replace('[://]', '://').replace('[:]', ':').replace('[', '').replace(']', '')
    ioc = ioc.replace('hxxp://', 'http://').replace('hxxps://', 'https://')
    return ioc.strip()

# Function to extract IOCs from Excel
def extract_iocs_from_excel(excel_file):
    df = pd.read_excel(excel_file)
    iocs = {'URL': [], 'FileHash-MD5': [], 'FileHash-SHA1': [], 'FileHash-SHA256': [], 'domain': [], 'hostname': [], 'ip': []}
    for index, row in df.iterrows():
        if not pd.isna(row.iloc[0]):
            ioc_type = str(row.iloc[0]).lower()
            ioc_value = str(row.iloc[1]).replace('[.]', '.')
            if 'domain' in ioc_type:
                iocs['domain'].append(ioc_value)
            elif 'url' in ioc_type:
                iocs['URL'].append(ioc_value)
            elif 'md5' in ioc_type:
                iocs['FileHash-MD5'].append(ioc_value)
            elif 'sha1' in ioc_type:
                iocs['FileHash-SHA1'].append(ioc_value)
            elif 'sha256' in ioc_type:
                iocs['FileHash-SHA256'].append(ioc_value)
            elif 'hostname' in ioc_type:
                iocs['hostname'].append(ioc_value)
            elif 'ip' in ioc_type:
                iocs['ip'].append(ioc_value)
    return iocs

# VirusTotal check function with additional details
def check_ioc_virustotal(ioc, ioc_type, api_key_manager, delay=2):
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        api_key = api_key_manager.get_api_key()
        if api_key is None:
            # All keys exhausted and none will reset soon
            return {
                "ioc": ioc,
                "result": "Error: All API keys exhausted",
                "malicious_count": 0,
                "software_info": {
                    "name": "Unknown",
                    "standardized_name": "Unknown",
                    "version": "Unknown",
                    "confidence": 0,
                    "metadata": {}
                },
                "engines_detected": [],
                "last_analyzed": "N/A"
            }
            
        headers = {"x-apikey": api_key}
        
        try:
            # Prepare URL based on IOC type
            if ioc_type == "URL":
                ioc_clean = normalize_ioc(ioc)
                ioc_encoded = base64.urlsafe_b64encode(ioc_clean.encode()).decode().strip("=")
                url = f"https://www.virustotal.com/api/v3/urls/{ioc_encoded}"
            elif ioc_type == "ip":
                ioc_clean = normalize_ioc(ioc)
                url = f"https://www.virustotal.com/api/v3/ip_addresses/{ioc_clean}"
            elif ioc_type in ["FileHash-MD5", "FileHash-SHA1", "FileHash-SHA256"]:
                ioc_clean = normalize_ioc(ioc)
                url = f"https://www.virustotal.com/api/v3/files/{ioc_clean}"
            elif ioc_type in ["domain", "hostname"]:
                ioc_clean = normalize_ioc(ioc)
                url = f"https://www.virustotal.com/api/v3/domains/{ioc_clean}"
            else:
                return {
                    "ioc": ioc,
                    "result": f"Unknown IOC type: {ioc_type}",
                    "malicious_count": 0,
                    "software_info": {
                        "name": "Unknown",
                        "standardized_name": "Unknown",
                        "version": "Unknown",
                        "confidence": 0,
                        "metadata": {}
                    },
                    "engines_detected": [],
                    "last_analyzed": "N/A"
                }
            
            tqdm.write(f"Using API key {api_key[:8]} for IOC: {ioc_clean}")
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                # Process successful response
                data = response.json()
                if "data" in data and "attributes" in data["data"]:
                    attributes = data["data"]["attributes"]
                    vendors = attributes.get("last_analysis_results", {})
                    malicious_count = sum(1 for v in vendors.values() if v["category"] == "malicious")
                    engines_detected = [vendor for vendor, result in vendors.items() if result["category"] == "malicious"]
                    software_info = extract_software_info(attributes, ioc_type)
                    last_analyzed = attributes.get("last_analysis_date", "N/A")
                    result_text = f"VirusTotal: Malicious ({malicious_count} vendors)" if malicious_count > 0 else "VirusTotal: Not malicious"
                    
                    return {
                        "ioc": ioc_clean,
                        "result": result_text,
                        "malicious_count": malicious_count,
                        "software_info": software_info,
                        "engines_detected": engines_detected,
                        "last_analyzed": last_analyzed
                    }
                    
            elif response.status_code in [429, 403]:  # Rate limit or quota exceeded
                tqdm.write(f"API key {api_key[:8]} rate limited. Rotating to next key...")
                api_key_manager.remove_api_key(api_key)
                continue
                
            elif response.status_code == 404:  # IOC not found
                return {
                    "ioc": ioc_clean,
                    "result": "VirusTotal: IOC not found",
                    "malicious_count": 0,
                    "software_info": {
                        "name": "Unknown",
                        "standardized_name": "Unknown",
                        "version": "Unknown",
                        "confidence": 0,
                        "metadata": {}
                    },
                    "engines_detected": [],
                    "last_analyzed": "N/A"
                }
            
            else:  # Other errors
                tqdm.write(f"Error {response.status_code} for IOC {ioc_clean}. Retrying...")
                retry_count += 1
                sleep(1)  # Short delay before retry
                continue
                
        except Exception as e:
            tqdm.write(f"Exception occurred while processing {ioc_clean}: {str(e)}")
            api_key_manager.remove_api_key(api_key)
            retry_count += 1
            sleep(1)  # Short delay before retry
            continue
    
    # If we've exhausted all retries
    return {
        "ioc": ioc_clean if 'ioc_clean' in locals() else ioc,
        "result": "Failed after multiple retries",
        "malicious_count": 0,
        "software_info": {
            "name": "Unknown",
            "standardized_name": "Unknown",
            "version": "Unknown",
            "confidence": 0,
            "metadata": {}
        },
        "engines_detected": [],
        "last_analyzed": "N/A"
    }

def extract_software_info(attributes, ioc_type):
    """Extract and standardize software information from VirusTotal attributes."""
    software_info = {
        "name": "Unknown",
        "standardized_name": "Unknown",
        "version": "Unknown",
        "confidence": 0,
        "metadata": {}
    }
    
    if ioc_type in ["FileHash-MD5", "FileHash-SHA1", "FileHash-SHA256"]:
        # Get meaningful names from various sources
        names = attributes.get("names", [])
        meaningful_name = attributes.get("meaningful_name", "")
        product_name = attributes.get("product", "")
        description = attributes.get("description", "")
        
        # Get file metadata
        file_type = attributes.get("type_tag", "")
        file_size = attributes.get("size", 0)
        creation_date = attributes.get("creation_date", "")
        
        # Get version information
        version_info = attributes.get("pe_info", {}).get("version_info", {})
        file_version = version_info.get("FileVersion", "")
        product_version = version_info.get("ProductVersion", "")
        original_name = version_info.get("OriginalFilename", "")
        company_name = version_info.get("CompanyName", "")
        
        # Get signature information
        signature_info = attributes.get("signature_info", {})
        publisher = signature_info.get("publisher", "")
        
        # Determine the most likely software name
        potential_names = [
            meaningful_name,
            product_name,
            version_info.get("ProductName", ""),
            version_info.get("FileDescription", ""),
            publisher
        ] + names
        
        potential_names = [n for n in potential_names if n]
        
        if potential_names:
            # Remove common unwanted suffixes and standardize
            clean_names = [standardize_software_name(n) for n in potential_names]
            
            # Get the most common standardized name
            from collections import Counter
            name_counts = Counter(clean_names)
            most_common_name = name_counts.most_common(1)[0][0]
            
            software_info["name"] = most_common_name
            software_info["standardized_name"] = standardize_software_name(most_common_name)
            software_info["version"] = file_version or product_version or "Unknown"
            software_info["confidence"] = calculate_name_confidence(attributes, most_common_name)
            software_info["metadata"] = {
                "file_type": file_type,
                "file_size": file_size,
                "creation_date": creation_date,
                "original_name": original_name,
                "description": description,
                "company_name": company_name,
                "publisher": publisher,
                "product_name": version_info.get("ProductName", ""),
                "file_description": version_info.get("FileDescription", ""),
                "is_signed": bool(signature_info)
            }
    
    return software_info

def standardize_software_name(name):
    """Standardize software names to a consistent format."""
    if not name:
        return "Unknown"
        
    # Convert to lowercase for processing
    name = name.lower()
    
    # Check against common software mappings first
    for pattern, standard_name in COMMON_SOFTWARE_MAPPINGS.items():
        if re.search(pattern, name, re.IGNORECASE):
            return standard_name
    
    # Remove common prefixes
    name = re.sub(r'^(the|a|an)\s+', '', name)
    
    # Remove file extensions
    name = re.sub(r'\.(exe|dll|sys|drv|com|bat|cmd|msi|scr)$', '', name)
    
    # Remove version numbers and common suffixes
    name = re.sub(r'\s+v?(\d+\.)*\d+(\.[A-Za-z]+)?$', '', name)
    name = re.sub(r'\s+(x86|x64|amd64|i386|setup|installer)$', '', name)
    
    # Remove special characters but keep hyphens and spaces
    name = re.sub(r'[^\w\s-]', '', name)
    
    # Remove multiple spaces and trim
    name = re.sub(r'\s+', ' ', name).strip()
    
    # Capitalize words, handling special cases
    words = name.split()
    capitalized_words = []
    for word in words:
        # Keep common abbreviations uppercase
        if word.upper() in ['USB', 'PDF', 'XML', 'HTML', 'CPU', 'GPU', 'API', 'SDK', 'CLI', 'GUI']:
            capitalized_words.append(word.upper())
        else:
            capitalized_words.append(word.capitalize())
    
    name = ' '.join(capitalized_words)
    
    return name

def calculate_name_confidence(attributes, chosen_name):
    """Calculate confidence score (0-100) for the chosen software name."""
    confidence = 0
    
    # Check if name appears in multiple sources (increased weights)
    if chosen_name in attributes.get("names", []):
        confidence += 25  # Increased from 30
    if chosen_name == attributes.get("meaningful_name", ""):
        confidence += 25  # Increased from 20
    if chosen_name == attributes.get("product", ""):
        confidence += 25  # Increased from 20
        
    # Check for strong identifiers
    version_info = attributes.get("pe_info", {}).get("version_info", {})
    if version_info:
        if version_info.get("CompanyName"):
            confidence += 10
        if version_info.get("ProductName"):
            confidence += 10
        if version_info.get("FileDescription"):
            confidence += 5
            
    # Check for digital signature
    if attributes.get("signature_info", {}):
        confidence += 15
        
    # Check for description
    if attributes.get("description", ""):
        confidence += 10
        
    # Bonus for matching known software patterns
    standardized_name = standardize_software_name(chosen_name)
    if standardized_name in COMMON_SOFTWARE_MAPPINGS.values():
        confidence += 15
        
    return min(confidence, 100)

# Process IOCs with VirusTotal
def process_iocs_concurrently(iocs, api_keys):
    results = []
    total_iocs = sum(len(ioc_list) for ioc_list in iocs.values())
    api_key_manager = APIKeyManager(api_keys)

    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_ioc = {}
        for ioc_type, ioc_list in iocs.items():
            for ioc in ioc_list:
                future = executor.submit(check_ioc_virustotal, ioc, ioc_type, api_key_manager)
                future_to_ioc[future] = (ioc, ioc_type)

        with tqdm(total=total_iocs, desc="Analyzing IOCs") as pbar:
            for future in as_completed(future_to_ioc):
                ioc, ioc_type = future_to_ioc[future]
                try:
                    result_dict = future.result()
                    software_info = result_dict["software_info"]
                    results.append({
                        "Type": ioc_type,
                        "IOC": result_dict["ioc"],
                        "Result": result_dict["result"],
                        "Malicious_Vendors": result_dict["malicious_count"],
                        "Software_Name": software_info["standardized_name"],
                        "Software_Version": software_info["version"],
                        "Confidence_Score": f"{software_info['confidence']}%",
                        "Original_Name": software_info["metadata"].get("original_name", "Unknown"),
                        "File_Type": software_info["metadata"].get("file_type", "Unknown"),
                        "Description": software_info["metadata"].get("description", "Unknown"),
                        "Engines_Detected": ", ".join(result_dict["engines_detected"]),
                        "Last_Analyzed": result_dict["last_analyzed"]
                    })
                    pbar.update(1)
                except Exception as exc:
                    tqdm.write(f"IOC {ioc} generated an exception: {exc}")
                    pbar.update(1)
    return results

# Function to sanitize strings for Excel compatibility
def sanitize_string(text):
    if not isinstance(text, str):  # Convert non-strings to strings safely
        return str(text) if text is not None else ""
    # Replace control characters and illegal characters with "-x-"
    cleaned = re.sub(r'[\x00-\x1F\x7F]', '-x-', text)
    return cleaned

# Function to sanitize all fields in results
def sanitize_results(results):
    for result in results:
        for key in result:
            result[key] = sanitize_string(result[key])
    return results

# Output to Excel with sanitization
def output_to_excel(results, output_file):
    cleaned_results = sanitize_results(results)
    df = pd.DataFrame(cleaned_results)
    with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
        df.to_excel(writer, index=False, sheet_name="IOC Results")
    print(f"Results have been saved to {output_file}")

# Main function with banner, description, and loading timer
def main():
    # Tunjuk banner dan description
    print(banner)
    print(description)

    # Simulasi loading timer (contoh: 3 saat)
    print("Memuatkan alat...")
    for _ in tqdm(range(3), desc="Loading", bar_format="{l_bar}{bar}"):
        time.sleep(1)
    print("Alat sudah sedia!\n")

    # Minta pengguna pilih fail
    root = Tk()
    root.withdraw()
    excel_file = filedialog.askopenfilename(
        title="Pilih fail Excel dengan IOCs",
        filetypes=[("Excel Files", "*.xlsx *.xls")]
    )
    if not excel_file:
        print("Tiada fail dipilih, keluar.")
        return

    # Proses IOCs
    iocs = extract_iocs_from_excel(excel_file)
    results = process_iocs_concurrently(iocs, api_keys)

    # Minta pengguna pilih lokasi simpan output
    output_file = filedialog.asksaveasfilename(
        title="Simpan Keputusan Sebagai",
        defaultextension=".xlsx",
        filetypes=[("Excel Files", "*.xlsx *.xls")]
    )
    if not output_file:
        print("Tiada fail output dipilih, keluar.")
        return

    output_to_excel(results, output_file)

if __name__ == "__main__":
    main()