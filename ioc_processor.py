"""
Refactored IOC Processing Module
Handles IOC extraction, normalization, and analysis with improved structure
"""

import re
import pandas as pd
import base64
import requests
import time
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import logging

from config import get_config
from api_manager import SecureAPIKeyManager
from error_handler import get_error_handler, DataProcessingError, APIError

logger = logging.getLogger(__name__)

class IOCType(Enum):
    """IOC Type enumeration"""
    URL = "URL"
    IP = "ip"
    DOMAIN = "domain"
    HOSTNAME = "hostname"
    FILE_HASH_MD5 = "FileHash-MD5"
    FILE_HASH_SHA1 = "FileHash-SHA1"
    FILE_HASH_SHA256 = "FileHash-SHA256"

@dataclass
class IOCData:
    """IOC data structure"""
    value: str
    ioc_type: IOCType
    original_value: str
    normalized: bool = False

@dataclass
class IOCAnalysisResult:
    """IOC analysis result structure"""
    ioc: str
    ioc_type: str
    result: str
    malicious_count: int
    app_names: List[str]
    hashes: Dict[str, str]
    detected_by_vendors: List[str]
    software_info: Dict[str, str]
    severity: str
    filename: str

class IOCNormalizer:
    """IOC normalization utilities"""
    
    @staticmethod
    def normalize_ioc(ioc: str) -> str:
        """
        Normalize IOC value by removing common obfuscation
        
        Args:
            ioc: Raw IOC value
            
        Returns:
            Normalized IOC value
        """
        if not ioc:
            return ""
        
        ioc = str(ioc).strip()
        
        # Remove common obfuscation patterns
        ioc = ioc.replace('[.]', '.').replace('(.)', '.')
        ioc = ioc.replace('[://]', '://').replace('[:]', ':')
        ioc = ioc.replace('[', '').replace(']', '')
        ioc = ioc.replace('hxxp://', 'http://').replace('hxxps://', 'https://')
        
        return ioc

class IOCTypeDetector:
    """IOC type detection utilities"""
    
    # Regex patterns for IOC detection
    IP_PATTERN = re.compile(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$')
    DOMAIN_PATTERN = re.compile(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$')
    MD5_PATTERN = re.compile(r'^[a-fA-F0-9]{32}$')
    SHA1_PATTERN = re.compile(r'^[a-fA-F0-9]{40}$')
    SHA256_PATTERN = re.compile(r'^[a-fA-F0-9]{64}$')
    
    @classmethod
    def detect_ioc_type(cls, ioc_value: str, type_hint: Optional[str] = None) -> IOCType:
        """
        Detect IOC type based on value and optional type hint
        
        Args:
            ioc_value: The IOC value
            type_hint: Optional type hint from input data
            
        Returns:
            Detected IOC type
        """
        ioc_value = ioc_value.strip()
        
        # Use type hint if provided and valid
        if type_hint:
            type_hint = type_hint.lower().strip()
            
            if 'url' in type_hint:
                return IOCType.URL
            elif 'domain' in type_hint:
                return IOCType.DOMAIN
            elif 'hostname' in type_hint:
                return IOCType.HOSTNAME
            elif 'ip' in type_hint:
                return IOCType.IP
            elif 'md5' in type_hint:
                return IOCType.FILE_HASH_MD5
            elif 'sha1' in type_hint:
                return IOCType.FILE_HASH_SHA1
            elif 'sha256' in type_hint:
                return IOCType.FILE_HASH_SHA256
        
        # Auto-detect based on value patterns
        if ioc_value.startswith(('http://', 'https://', 'ftp://')):
            return IOCType.URL
        elif cls.IP_PATTERN.match(ioc_value):
            return IOCType.IP
        elif cls.SHA256_PATTERN.match(ioc_value):
            return IOCType.FILE_HASH_SHA256
        elif cls.SHA1_PATTERN.match(ioc_value):
            return IOCType.FILE_HASH_SHA1
        elif cls.MD5_PATTERN.match(ioc_value):
            return IOCType.FILE_HASH_MD5
        elif cls.DOMAIN_PATTERN.match(ioc_value) and '.' in ioc_value:
            return IOCType.DOMAIN
        else:
            return IOCType.HOSTNAME

class IOCExtractor:
    """Extract and process IOCs from Excel files"""
    
    def __init__(self):
        self.normalizer = IOCNormalizer()
        self.type_detector = IOCTypeDetector()
        self.error_handler = get_error_handler()
    
    def extract_from_excel(self, excel_file: str) -> Tuple[Dict[str, List[str]], int, int]:
        """
        Extract IOCs from Excel file
        
        Args:
            excel_file: Path to Excel file
            
        Returns:
            Tuple of (IOCs dict, total_read, duplicates_skipped)
        """
        try:
            df = pd.read_excel(excel_file)
        except Exception as e:
            self.error_handler.handle_error(
                DataProcessingError(f"Failed to read Excel file: {e}"),
                {"file": excel_file}
            )
            return {}, 0, 0
        
        # Validate file structure
        if len(df.columns) < 2:
            error_msg = f"Excel file must have at least 2 columns. Found {len(df.columns)}"
            self.error_handler.handle_error(
                DataProcessingError(error_msg),
                {"file": excel_file, "columns": list(df.columns)}
            )
            return {}, 0, 0
        
        return self._process_dataframe(df)
    
    def _process_dataframe(self, df: pd.DataFrame) -> Tuple[Dict[str, List[str]], int, int]:
        """
        Process DataFrame to extract IOCs
        
        Args:
            df: Pandas DataFrame
            
        Returns:
            Tuple of (IOCs dict, total_read, duplicates_skipped)
        """
        iocs = {ioc_type.value: [] for ioc_type in IOCType}
        seen_iocs = set()
        total_read = 0
        duplicates_skipped = 0
        
        type_col_index = 0
        ioc_col_index = 1
        
        for index, row in df.iterrows():
            # Skip rows with missing data
            if pd.isna(row.iloc[type_col_index]) or pd.isna(row.iloc[ioc_col_index]):
                continue
            
            total_read += 1
            
            # Extract and normalize IOC
            type_hint = str(row.iloc[type_col_index]).strip()
            raw_ioc = str(row.iloc[ioc_col_index]).strip()
            normalized_ioc = self.normalizer.normalize_ioc(raw_ioc)
            
            if not normalized_ioc:
                continue
            
            # Check for duplicates
            if normalized_ioc in seen_iocs:
                duplicates_skipped += 1
                continue
            
            seen_iocs.add(normalized_ioc)
            
            # Detect IOC type and categorize
            ioc_type = self.type_detector.detect_ioc_type(normalized_ioc, type_hint)
            
            # Additional validation for specific types
            if self._validate_ioc(normalized_ioc, ioc_type):
                iocs[ioc_type.value].append(normalized_ioc)
        
        return iocs, total_read, duplicates_skipped
    
    def _validate_ioc(self, ioc_value: str, ioc_type: IOCType) -> bool:
        """
        Validate IOC value against its type
        
        Args:
            ioc_value: IOC value to validate
            ioc_type: Expected IOC type
            
        Returns:
            True if valid, False otherwise
        """
        try:
            if ioc_type == IOCType.IP:
                # Basic IP validation
                parts = ioc_value.split('.')
                if len(parts) != 4:
                    return False
                for part in parts:
                    if not (0 <= int(part) <= 255):
                        return False
                return True
            
            elif ioc_type in [IOCType.FILE_HASH_MD5, IOCType.FILE_HASH_SHA1, IOCType.FILE_HASH_SHA256]:
                # Hash validation
                expected_lengths = {
                    IOCType.FILE_HASH_MD5: 32,
                    IOCType.FILE_HASH_SHA1: 40,
                    IOCType.FILE_HASH_SHA256: 64
                }
                return len(ioc_value) == expected_lengths[ioc_type]
            
            elif ioc_type == IOCType.URL:
                # Basic URL validation
                return ioc_value.startswith(('http://', 'https://'))
            
            elif ioc_type in [IOCType.DOMAIN, IOCType.HOSTNAME]:
                # Domain/hostname validation
                return '.' in ioc_value and len(ioc_value) > 3
            
            return True
            
        except (ValueError, AttributeError):
            return False

class SoftwareClassifier:
    """Classify and standardize software names"""

    LEGITIMATE_PATTERNS = {
        r'microsoft|msft': 'Microsoft',
        r'chrome|google': 'Google',
        r'firefox|mozilla': 'Mozilla',
        r'adobe': 'Adobe',
        r'oracle|java': 'Oracle',
        r'vmware': 'VMware',
        r'python': 'Python',
        r'node\.?js': 'Node.js',
        r'winzip|7zip|7-zip': 'Compression Tool',
        r'notepad\+\+': 'Notepad++',
        r'visual studio|vscode': 'Visual Studio'
    }

    @classmethod
    def classify_software(cls, names: List[str], malicious_count: int) -> Dict[str, str]:
        """
        Classify software based on names and malicious detection count

        Args:
            names: List of software names
            malicious_count: Number of malicious detections

        Returns:
            Classification result dictionary
        """
        if not names:
            return {
                "standard_name": "Unknown",
                "confidence": "Low",
                "classification": "Unknown"
            }

        names_lower = [name.lower() for name in names if name]

        # Check for legitimate software patterns
        for pattern, vendor in cls.LEGITIMATE_PATTERNS.items():
            if any(re.search(pattern, name) for name in names_lower):
                classification = cls._determine_classification(malicious_count, is_known_software=True)
                return {
                    "standard_name": f"{vendor} Software",
                    "confidence": "High" if malicious_count == 0 else "Medium",
                    "classification": classification
                }

        # Handle unknown software
        most_common = max(set(names), key=names.count) if names else "Unknown"
        cleaned_name = re.sub(r'[^\w\s\-\.]', '', most_common).strip()
        cleaned_name = re.sub(r'\s+', ' ', cleaned_name)

        classification = cls._determine_classification(malicious_count, is_known_software=False)

        return {
            "standard_name": cleaned_name.title() if cleaned_name else "Unknown",
            "confidence": cls._determine_confidence(malicious_count),
            "classification": classification
        }

    @staticmethod
    def _determine_classification(malicious_count: int, is_known_software: bool) -> str:
        """Determine classification based on malicious count and software knowledge"""
        if malicious_count > 5:
            return "Malicious"
        elif malicious_count > 0:
            if is_known_software:
                return "Suspicious (Low Risk)" if malicious_count < 3 else "Suspicious (High Risk)"
            else:
                return "Suspicious"
        elif is_known_software:
            return "Legitimate"
        else:
            return "Unknown"

    @staticmethod
    def _determine_confidence(malicious_count: int) -> str:
        """Determine confidence level based on malicious count"""
        if malicious_count > 5:
            return "High"
        elif malicious_count > 0:
            return "Medium"
        else:
            return "Low"


class VirusTotalClient:
    """VirusTotal API client with improved error handling"""

    def __init__(self, api_key_manager: SecureAPIKeyManager, request_timeout: int = 30):
        """
        Initialize VirusTotal client

        Args:
            api_key_manager: API key manager instance
            request_timeout: Request timeout in seconds
        """
        self.api_key_manager = api_key_manager
        self.request_timeout = request_timeout
        self.error_handler = get_error_handler()
        self.software_classifier = SoftwareClassifier()

    def analyze_ioc(self, ioc: str, ioc_type: IOCType, selected_vendors: Optional[List[str]] = None) -> IOCAnalysisResult:
        """
        Analyze IOC using VirusTotal API

        Args:
            ioc: IOC value to analyze
            ioc_type: Type of IOC
            selected_vendors: Optional list of vendors to filter results

        Returns:
            IOC analysis result
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            api_key = self.api_key_manager.get_api_key()
            if not api_key:
                return self._create_error_result(ioc, ioc_type, "No API keys available")

            try:
                return self._make_api_request(ioc, ioc_type, api_key, selected_vendors)

            except APIError as e:
                retry_count += 1
                if "401" in str(e) or "invalid" in str(e).lower():
                    self.api_key_manager.mark_key_invalid(api_key, str(e))
                elif "429" in str(e) or "quota" in str(e).lower():
                    self.api_key_manager.mark_key_exhausted(api_key, str(e))
                else:
                    self.api_key_manager.mark_key_rate_limited(api_key)

                if retry_count >= max_retries:
                    return self._create_error_result(ioc, ioc_type, f"Max retries exceeded: {e}")

            except Exception as e:
                self.error_handler.handle_error(e, {"ioc": ioc, "type": ioc_type.value})
                return self._create_error_result(ioc, ioc_type, f"Unexpected error: {e}")

        return self._create_error_result(ioc, ioc_type, "Analysis failed after retries")

    def _make_api_request(self, ioc: str, ioc_type: IOCType, api_key: str, selected_vendors: Optional[List[str]]) -> IOCAnalysisResult:
        """Make the actual API request to VirusTotal"""

        # Build URL based on IOC type
        url = self._build_api_url(ioc, ioc_type)
        headers = {"x-apikey": api_key}

        try:
            response = requests.get(url, headers=headers, timeout=self.request_timeout)

            if response.status_code == 200:
                return self._process_successful_response(response.json(), ioc, ioc_type, selected_vendors)
            elif response.status_code == 404:
                return self._create_not_found_result(ioc, ioc_type)
            elif response.status_code in [401, 403, 429]:
                raise APIError(f"HTTP {response.status_code}: {response.text}")
            else:
                raise APIError(f"HTTP {response.status_code}: {response.text}")

        except requests.exceptions.Timeout:
            raise APIError("Request timeout")
        except requests.exceptions.ConnectionError:
            raise APIError("Connection error")
        except requests.exceptions.RequestException as e:
            raise APIError(f"Request failed: {e}")

    def _build_api_url(self, ioc: str, ioc_type: IOCType) -> str:
        """Build VirusTotal API URL based on IOC type"""
        base_url = "https://www.virustotal.com/api/v3"

        if ioc_type == IOCType.URL:
            ioc_encoded = base64.urlsafe_b64encode(ioc.encode()).decode().strip("=")
            return f"{base_url}/urls/{ioc_encoded}"
        elif ioc_type == IOCType.IP:
            return f"{base_url}/ip_addresses/{ioc}"
        elif ioc_type in [IOCType.FILE_HASH_MD5, IOCType.FILE_HASH_SHA1, IOCType.FILE_HASH_SHA256]:
            return f"{base_url}/files/{ioc}"
        elif ioc_type in [IOCType.DOMAIN, IOCType.HOSTNAME]:
            return f"{base_url}/domains/{ioc}"
        else:
            raise ValueError(f"Unsupported IOC type: {ioc_type}")

    def _process_successful_response(self, data: Dict[str, Any], ioc: str, ioc_type: IOCType, selected_vendors: Optional[List[str]]) -> IOCAnalysisResult:
        """Process successful VirusTotal API response"""

        if "data" not in data or "attributes" not in data["data"]:
            return self._create_error_result(ioc, ioc_type, "Invalid response format")

        attributes = data["data"]["attributes"]
        all_vendors_results = attributes.get("last_analysis_results", {})

        # Filter vendors if specified
        if selected_vendors and selected_vendors != "All":
            selected_vendors_lower = {v.lower() for v in selected_vendors}
            vendors_results = {
                vendor: result for vendor, result in all_vendors_results.items()
                if vendor.lower() in selected_vendors_lower
            }
        else:
            vendors_results = all_vendors_results

        # Count malicious detections
        malicious_count = sum(1 for result in vendors_results.values()
                            if result.get("category") == "malicious")

        detecting_vendors = [vendor for vendor, result in vendors_results.items()
                           if result.get("category") == "malicious"]

        # Get software information for file hashes
        app_names = []
        hashes = {}
        if ioc_type in [IOCType.FILE_HASH_MD5, IOCType.FILE_HASH_SHA1, IOCType.FILE_HASH_SHA256]:
            app_names = attributes.get("names", [])
            hashes = {
                'sha256': attributes.get('sha256'),
                'sha1': attributes.get('sha1'),
                'md5': attributes.get('md5')
            }
            hashes = {k: v for k, v in hashes.items() if v}

        # Classify software
        software_info = self.software_classifier.classify_software(app_names, malicious_count)

        # Determine result text
        result_text = f"Malicious (detected by {malicious_count} vendors)" if malicious_count > 0 else "Clean"

        return IOCAnalysisResult(
            ioc=ioc,
            ioc_type=ioc_type.value,
            result=result_text,
            malicious_count=malicious_count,
            app_names=app_names or ["N/A"],
            hashes=hashes,
            detected_by_vendors=detecting_vendors,
            software_info=software_info,
            severity=self._calculate_severity(software_info, malicious_count),
            filename=self._extract_filename(app_names)
        )

    def _create_error_result(self, ioc: str, ioc_type: IOCType, error_message: str) -> IOCAnalysisResult:
        """Create error result"""
        return IOCAnalysisResult(
            ioc=ioc,
            ioc_type=ioc_type.value,
            result=f"Error: {error_message}",
            malicious_count=0,
            app_names=["N/A"],
            hashes={},
            detected_by_vendors=[],
            software_info={"standard_name": "Unknown", "confidence": "Low", "classification": "Error"},
            severity="Low",
            filename="Unknown"
        )

    def _create_not_found_result(self, ioc: str, ioc_type: IOCType) -> IOCAnalysisResult:
        """Create not found result"""
        return IOCAnalysisResult(
            ioc=ioc,
            ioc_type=ioc_type.value,
            result="Not found in VirusTotal",
            malicious_count=0,
            app_names=["N/A"],
            hashes={},
            detected_by_vendors=[],
            software_info={"standard_name": "Unknown", "confidence": "Low", "classification": "Not Found"},
            severity="Low",
            filename="Unknown"
        )

    @staticmethod
    def _calculate_severity(software_info: Dict[str, str], malicious_count: int) -> str:
        """Calculate severity based on classification and malicious count"""
        classification = software_info.get("classification", "Unknown")

        if classification == "Malicious" or malicious_count >= 5:
            return "High"
        elif classification == "Legitimate" or malicious_count == 0:
            return "Very Low"
        elif "Error" in classification or "Not Found" in classification:
            return "Low"
        else:
            return "Medium"

    @staticmethod
    def _extract_filename(app_names: List[str]) -> str:
        """Extract filename from app names"""
        if app_names and app_names != ["N/A"]:
            valid_names = [name for name in app_names if name and isinstance(name, str)]
            if valid_names:
                return valid_names[0]
        return "Unknown"
