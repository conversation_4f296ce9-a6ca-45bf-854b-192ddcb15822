"""
Comprehensive error handling and logging system for IOC Analysis Tool
"""

import logging
import sys
import traceback
from typing import Optional, Dict, Any, Callable
from functools import wraps
from pathlib import Path
from datetime import datetime
import json

class IOCAnalysisError(Exception):
    """Base exception for IOC Analysis Tool"""
    pass

class ConfigurationError(IOCAnalysisError):
    """Configuration related errors"""
    pass

class APIError(IOCAnalysisError):
    """API related errors"""
    pass

class DataProcessingError(IOCAnalysisError):
    """Data processing related errors"""
    pass

class FileHandlingError(IOCAnalysisError):
    """File handling related errors"""
    pass

class ErrorHandler:
    """Centralized error handling and logging"""
    
    def __init__(self, log_dir: str = "logs", log_level: int = logging.INFO):
        """
        Initialize error handler
        
        Args:
            log_dir: Directory for log files
            log_level: Logging level
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Setup logging
        self._setup_logging(log_level)
        
        # Error statistics
        self.error_stats = {
            "total_errors": 0,
            "error_types": {},
            "session_start": datetime.now().isoformat()
        }
    
    def _setup_logging(self, log_level: int) -> None:
        """Setup logging configuration"""
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler for detailed logs
        log_file = self.log_dir / f"ioc_analysis_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(file_handler)
        
        # Error file handler
        error_file = self.log_dir / f"errors_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.FileHandler(error_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(error_handler)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Error handling system initialized")
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle and log an error with context
        
        Args:
            error: The exception that occurred
            context: Additional context information
            
        Returns:
            Error information dictionary
        """
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat(),
            "context": context or {},
            "traceback": traceback.format_exc()
        }
        
        # Update statistics
        self.error_stats["total_errors"] += 1
        error_type = error_info["error_type"]
        self.error_stats["error_types"][error_type] = self.error_stats["error_types"].get(error_type, 0) + 1
        
        # Log the error
        self.logger.error(
            f"Error occurred: {error_info['error_type']} - {error_info['error_message']}"
        )
        
        if context:
            self.logger.error(f"Context: {json.dumps(context, indent=2)}")
        
        self.logger.debug(f"Full traceback:\n{error_info['traceback']}")
        
        return error_info
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        return self.error_stats.copy()
    
    def save_error_report(self, filename: Optional[str] = None) -> str:
        """
        Save error report to file
        
        Args:
            filename: Optional filename for the report
            
        Returns:
            Path to the saved report
        """
        if filename is None:
            filename = f"error_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_path = self.log_dir / filename
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.error_stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Error report saved to {report_path}")
            return str(report_path)
            
        except Exception as e:
            self.logger.error(f"Failed to save error report: {e}")
            raise

def error_handler_decorator(error_handler: ErrorHandler, 
                          reraise: bool = True, 
                          default_return: Any = None):
    """
    Decorator for automatic error handling
    
    Args:
        error_handler: ErrorHandler instance
        reraise: Whether to reraise the exception after handling
        default_return: Default return value if exception is not reraised
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "args": str(args)[:200],  # Limit length
                    "kwargs": str(kwargs)[:200]  # Limit length
                }
                
                error_handler.handle_error(e, context)
                
                if reraise:
                    raise
                else:
                    return default_return
        
        return wrapper
    return decorator

def safe_execute(func: Callable, 
                error_handler: ErrorHandler,
                default_return: Any = None,
                context: Optional[Dict[str, Any]] = None) -> Any:
    """
    Safely execute a function with error handling
    
    Args:
        func: Function to execute
        error_handler: ErrorHandler instance
        default_return: Default return value on error
        context: Additional context for error reporting
        
    Returns:
        Function result or default_return on error
    """
    try:
        return func()
    except Exception as e:
        error_handler.handle_error(e, context)
        return default_return

class UserFriendlyErrorReporter:
    """Convert technical errors to user-friendly messages"""
    
    ERROR_MESSAGES = {
        "ConfigurationError": "Configuration issue detected. Please check your settings.",
        "APIError": "API communication error. Please check your internet connection and API keys.",
        "DataProcessingError": "Error processing data. Please check your input file format.",
        "FileHandlingError": "File operation failed. Please check file permissions and disk space.",
        "ConnectionError": "Network connection error. Please check your internet connection.",
        "TimeoutError": "Operation timed out. Please try again or check your network connection.",
        "PermissionError": "Permission denied. Please check file permissions.",
        "FileNotFoundError": "File not found. Please check the file path.",
        "JSONDecodeError": "Invalid configuration file format. Please check your JSON syntax."
    }
    
    @classmethod
    def get_user_message(cls, error: Exception) -> str:
        """
        Get user-friendly error message
        
        Args:
            error: The exception
            
        Returns:
            User-friendly error message
        """
        error_type = type(error).__name__
        
        # Check for specific error types
        if error_type in cls.ERROR_MESSAGES:
            return cls.ERROR_MESSAGES[error_type]
        
        # Check for common Python exceptions
        if "Connection" in error_type:
            return cls.ERROR_MESSAGES["ConnectionError"]
        elif "Timeout" in error_type:
            return cls.ERROR_MESSAGES["TimeoutError"]
        elif "Permission" in error_type:
            return cls.ERROR_MESSAGES["PermissionError"]
        elif "FileNotFound" in error_type:
            return cls.ERROR_MESSAGES["FileNotFoundError"]
        elif "JSON" in error_type:
            return cls.ERROR_MESSAGES["JSONDecodeError"]
        
        # Generic message for unknown errors
        return f"An unexpected error occurred: {str(error)[:100]}..."

# Global error handler instance
error_handler = ErrorHandler()

def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance"""
    return error_handler
