# IOC Analysis Tool Requirements
# Core dependencies for the IOC analysis tool

# Data processing and analysis
pandas>=1.5.0
openpyxl>=3.0.10

# HTTP requests and API communication
requests>=2.28.0

# GUI framework
tkinter  # Usually included with Python

# Progress bars and CLI utilities
tqdm>=4.64.0

# Development and testing dependencies
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.8.0

# Type checking and code quality
mypy>=0.991
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0

# Documentation
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0

# Security scanning
bandit>=1.7.0
safety>=2.0.0
